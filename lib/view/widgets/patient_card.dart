import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/theme/custom_text_style.dart';
import 'package:platix/theme/app_decoration.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';

class PatientCard extends StatelessWidget {
  final PatientData patient;
  final int index;
  final VoidCallback? onDelete;
  final PermissionService permissionService;

  const PatientCard({
    super.key,
    required this.patient,
    required this.index,
    required this.permissionService,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadiusStyle.radius8,
        boxShadow: AppDecoration.shadow1_3,
        border: Border.all(
          color: AppColors.primary2.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadiusStyle.radius8,
          onTap: () {
            // Optional: Navigate to patient details or edit screen
            if (permissionService.hasPermission('Registration', 'is_edit')) {
              Get.to(() => CreatePatientRegistrationScreen(
                patientData: patient,
              ));
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row with Avatar and Actions
                Row(
                  children: [
                    // Patient Avatar
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          patient.displayName.isNotEmpty 
                              ? patient.displayName[0].toUpperCase()
                              : 'P',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Patient Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            patient.displayName,
                            style: CustomTextStyles.b3_1.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.black,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          if (patient.patientRegId != null)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary2,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'ID: ${patient.patientRegId}',
                                style: CustomTextStyles.b5_1.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // Action Buttons
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (permissionService.hasPermission('Registration', 'is_edit'))
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.primary2,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.edit_outlined,
                                color: AppColors.primary,
                                size: 20,
                              ),
                              onPressed: () {
                                Get.to(() => CreatePatientRegistrationScreen(
                                  patientData: patient,
                                ));
                              },
                              tooltip: 'Edit Patient',
                            ),
                          ),
                        const SizedBox(width: 8),
                        if (permissionService.hasPermission('Registration', 'is_delete'))
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.lightRed,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.delete_outline,
                                color: AppColors.red,
                                size: 20,
                              ),
                              onPressed: () => _showDeleteDialog(context),
                              tooltip: 'Delete Patient',
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Patient Details Grid
                _buildInfoGrid(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoGrid() {
    final List<Map<String, String?>> infoItems = [
      {'icon': '📧', 'label': 'Email', 'value': patient.email},
      {'icon': '📱', 'label': 'Mobile', 'value': patient.mobile},
      {'icon': '👤', 'label': 'Gender', 'value': patient.gender},
      {'icon': '📅', 'label': 'Registered', 'value': patient.formattedDate},
    ];

    return Column(
      children: [
        for (int i = 0; i < infoItems.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    infoItems[i]['icon']!,
                    infoItems[i]['label']!,
                    infoItems[i]['value'],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: i + 1 < infoItems.length
                      ? _buildInfoItem(
                          infoItems[i + 1]['icon']!,
                          infoItems[i + 1]['label']!,
                          infoItems[i + 1]['value'],
                        )
                      : const SizedBox(),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildInfoItem(String icon, String label, String? value) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.background2.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: AppColors.primary2.withValues(alpha: 0.2),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Text(
            icon,
            style: const TextStyle(fontSize: 14),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: CustomTextStyles.b5_1.copyWith(
                    color: AppColors.darkerGrey,
                    fontSize: 10,
                  ),
                ),
                Text(
                  value ?? 'N/A',
                  style: CustomTextStyles.b5_1.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    Get.defaultDialog(
      title: 'Delete Patient',
      titleStyle: const TextStyle(
        color: AppColors.primary,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      middleText: 'Are you sure you want to delete patient ${patient.displayName}?',
      middleTextStyle: const TextStyle(
        color: AppColors.black,
        fontSize: 14,
      ),
      backgroundColor: AppColors.white,
      radius: 12,
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      cancelTextColor: AppColors.primary,
      buttonColor: AppColors.primary,
      onConfirm: () {
        Get.back();
        if (onDelete != null) {
          onDelete!();
        }
      },
      onCancel: () {
        Get.back();
      },
    );
  }
}
