import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/theme/custom_text_style.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';

class PatientCard extends StatelessWidget {
  final PatientData patient;
  final int index;
  final VoidCallback? onDelete;
  final PermissionService permissionService;

  const PatientCard({
    super.key,
    required this.patient,
    required this.index,
    required this.permissionService,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.12),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: AppColors.black.withValues(alpha: 0.04),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: AppColors.primary2.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // Optional: Navigate to patient details or edit screen
            if (permissionService.hasPermission('Registration', 'is_edit')) {
              Get.to(() => CreatePatientRegistrationScreen(
                patientData: patient,
              ));
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Row with Avatar and Actions
                Row(
                  children: [
                    // Patient Avatar
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColors.primary,
                            AppColors.primary3,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withValues(alpha: 0.25),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Text(
                          patient.displayName.isNotEmpty
                              ? patient.displayName[0].toUpperCase()
                              : 'P',
                          style: const TextStyle(
                            color: AppColors.white,
                            fontSize: 22,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Patient Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            patient.displayName,
                            style: CustomTextStyles.b3_1.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.black,
                              fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 6),
                          if (patient.patientRegId != null)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.primary2.withValues(alpha: 0.8),
                                    AppColors.primary2.withValues(alpha: 0.6),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: AppColors.primary.withValues(alpha: 0.3),
                                  width: 0.5,
                                ),
                              ),
                              child: Text(
                                'ID: ${patient.patientRegId}',
                                style: CustomTextStyles.b5_1.copyWith(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 11,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    // Action Buttons
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (permissionService.hasPermission('Registration', 'is_edit'))
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.primary.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppColors.primary.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.edit_outlined,
                                color: AppColors.primary,
                                size: 18,
                              ),
                              onPressed: () {
                                Get.to(() => CreatePatientRegistrationScreen(
                                  patientData: patient,
                                ));
                              },
                              tooltip: 'Edit Patient',
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 36,
                                minHeight: 36,
                              ),
                            ),
                          ),
                        const SizedBox(width: 8),
                        if (permissionService.hasPermission('Registration', 'is_delete'))
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.red.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppColors.red.withValues(alpha: 0.2),
                                width: 1,
                              ),
                            ),
                            child: IconButton(
                              icon: const Icon(
                                Icons.delete_outline,
                                color: AppColors.red,
                                size: 18,
                              ),
                              onPressed: () => _showDeleteDialog(context),
                              tooltip: 'Delete Patient',
                              padding: const EdgeInsets.all(8),
                              constraints: const BoxConstraints(
                                minWidth: 36,
                                minHeight: 36,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Patient Details Grid
                _buildInfoGrid(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoGrid() {
    final List<Map<String, dynamic>> infoItems = [
      {'icon': Icons.email_outlined, 'label': 'Email', 'value': patient.email},
      {'icon': Icons.phone_outlined, 'label': 'Mobile', 'value': patient.mobile},
      {'icon': Icons.person_outline, 'label': 'Gender', 'value': patient.gender},
      {'icon': Icons.calendar_today_outlined, 'label': 'Registered', 'value': patient.formattedDate},
    ];

    return Column(
      children: [
        for (int i = 0; i < infoItems.length; i += 2)
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    infoItems[i]['icon'] as IconData,
                    infoItems[i]['label'] as String,
                    infoItems[i]['value'] as String?,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: i + 1 < infoItems.length
                      ? _buildInfoItem(
                          infoItems[i + 1]['icon'] as IconData,
                          infoItems[i + 1]['label'] as String,
                          infoItems[i + 1]['value'] as String?,
                        )
                      : const SizedBox(),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String? value) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.primary2.withValues(alpha: 0.4),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.08),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: AppColors.primary2.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              icon,
              size: 16,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: CustomTextStyles.b5_1.copyWith(
                    color: AppColors.darkerGrey,
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value ?? 'N/A',
                  style: CustomTextStyles.b5_1.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context) {
    Get.defaultDialog(
      title: 'Delete Patient',
      titleStyle: const TextStyle(
        color: AppColors.primary,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
      middleText: 'Are you sure you want to delete patient ${patient.displayName}?',
      middleTextStyle: const TextStyle(
        color: AppColors.black,
        fontSize: 14,
      ),
      backgroundColor: AppColors.white,
      radius: 12,
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      cancelTextColor: AppColors.primary,
      buttonColor: AppColors.primary,
      onConfirm: () {
        Get.back();
        if (onDelete != null) {
          onDelete!();
        }
      },
      onCancel: () {
        Get.back();
      },
    );
  }
}
