import 'package:flutter/material.dart';
import 'package:country_state_city/country_state_city.dart' as csc;
import 'package:platix/utils/app_export.dart';

class EnhancedLocationSelector extends StatefulWidget {
  final String? initialCountryId;
  final String? initialStateId;
  final String? initialCityName;
  final Function(String? countryId, String countryName) onCountryChanged;
  final Function(String? stateId, String stateName) onStateChanged;
  final Function(String cityName) onCityChanged;

  const EnhancedLocationSelector({
    super.key,
    this.initialCountryId,
    this.initialStateId,
    this.initialCityName,
    required this.onCountryChanged,
    required this.onStateChanged,
    required this.onCityChanged,
  });

  @override
  State<EnhancedLocationSelector> createState() => _EnhancedLocationSelectorState();
}

class _EnhancedLocationSelectorState extends State<EnhancedLocationSelector> {
  List<csc.Country> countries = [];
  List<csc.State> states = [];
  List<csc.City> cities = [];
  
  csc.Country? selectedCountry;
  csc.State? selectedState;
  String? selectedCityName;
  
  bool isLoadingCountries = true;
  bool isLoadingStates = false;
  bool isLoadingCities = false;

  @override
  void initState() {
    super.initState();
    _loadCountries();
  }

  Future<void> _loadCountries() async {
    setState(() => isLoadingCountries = true);
    try {
      countries = await csc.getAllCountries();
      
      // Set initial country if provided
      if (widget.initialCountryId != null) {
        selectedCountry = countries.firstWhere(
          (country) => country.isoCode == widget.initialCountryId,
          orElse: () => countries.first,
        );
        await _loadStates();
      }
    } catch (e) {
      debugPrint('Error loading countries: $e');
    } finally {
      setState(() => isLoadingCountries = false);
    }
  }

  Future<void> _loadStates() async {
    if (selectedCountry == null) return;
    
    setState(() => isLoadingStates = true);
    try {
      states = await csc.getStatesOfCountry(selectedCountry!.isoCode);
      
      // Set initial state if provided
      if (widget.initialStateId != null) {
        selectedState = states.firstWhere(
          (state) => state.isoCode == widget.initialStateId,
          orElse: () => states.isNotEmpty ? states.first : csc.State(
            name: '', 
            isoCode: '', 
            countryCode: selectedCountry?.isoCode ?? ''
          ),
        );
        if (selectedState != null && selectedState!.isoCode.isNotEmpty) {
          await _loadCities();
        }
      } else {
        selectedState = null;
        cities = [];
        selectedCityName = null;
      }
    } catch (e) {
      debugPrint('Error loading states: $e');
    } finally {
      setState(() => isLoadingStates = false);
    }
  }

  Future<void> _loadCities() async {
    if (selectedCountry == null || selectedState == null) return;

    setState(() => isLoadingCities = true);
    try {
      final stateCities = await csc.getStateCities(selectedState!.countryCode, selectedState!.isoCode);
      cities = stateCities;

      // Set initial city if provided
      if (widget.initialCityName != null) {
        final initialCity = cities.firstWhere(
          (city) => city.name == widget.initialCityName,
          orElse: () => cities.isNotEmpty ? cities.first : csc.City(name: '', countryCode: '', stateCode: ''),
        );
        if (initialCity.name.isNotEmpty) {
          selectedCityName = initialCity.name;
        } else {
          selectedCityName = null;
        }
      } else {
        selectedCityName = null;
      }

      debugPrint('Cities loaded: ${cities.length} cities for state: ${selectedState!.name}');
    } catch (e) {
      debugPrint('Error loading cities: $e');
      cities = [];
      selectedCityName = null;
    } finally {
      setState(() => isLoadingCities = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // State Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('State', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: selectedCountry != null ? () => _showStateDialog() : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: selectedCountry != null ? Colors.white : Colors.grey.shade100,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    isLoadingStates
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            selectedState?.name ?? 'Select State',
                            style: TextStyle(
                              color: selectedState != null ? Colors.black : Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 15),
        
        // City Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('City', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: selectedState != null ? () => _showCityDialog() : null,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: selectedState != null ? Colors.white : Colors.grey.shade100,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    isLoadingCities
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            selectedCityName ?? 'Enter City Name',
                            style: TextStyle(
                              color: selectedCityName != null ? Colors.black : Colors.grey,
                              fontSize: 14,
                            ),
                          ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 15),

        // Country Dropdown
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Country', style: CustomTextStyles.b4_1),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () => _showCountryDialog(),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      selectedCountry?.name ?? 'Select Country',
                      style: TextStyle(
                        color: selectedCountry != null ? Colors.black : Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showCountryDialog() {
    if (isLoadingCountries) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _SearchableDialog<csc.Country>(
          title: 'Select Country',
          items: countries,
          itemBuilder: (country) => country.name,
          searchHint: 'Search countries...',
          onItemSelected: (country) {
            setState(() {
              selectedCountry = country;
              selectedState = null;
              selectedCityName = null;
              states = [];
              cities = [];
            });
            widget.onCountryChanged(country.isoCode, country.name);
            _loadStates();
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _showStateDialog() {
    if (isLoadingStates) return;

    if (states.isEmpty) {
      // If no states available, show a message
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('No States Available'),
            content: const Text('No states/provinces are available for the selected country. You can proceed to enter the city directly.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _SearchableDialog<csc.State>(
          title: 'Select State',
          items: states,
          itemBuilder: (state) => state.name,
          searchHint: 'Search states...',
          onItemSelected: (state) {
            setState(() {
              selectedState = state;
              selectedCityName = null;
              cities = [];
            });
            widget.onStateChanged(state.isoCode, state.name);
            _loadCities();
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  void _showCityDialog() {
    if (isLoadingCities) return;

    if (cities.isEmpty) {
      // Show message that no cities are available
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('No Cities Available'),
            content: const Text('No cities are available for the selected state.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return _SearchableDialog<csc.City>(
          title: 'Select City (${cities.length} available)',
          items: cities,
          itemBuilder: (city) => city.name,
          searchHint: 'Search cities...',
          onItemSelected: (city) {
            setState(() {
              selectedCityName = city.name;
            });
            widget.onCityChanged(city.name);
            Navigator.of(context).pop();
          },
        );
      },
    );
  }
}

class _SearchableDialog<T> extends StatefulWidget {
  final String title;
  final List<T> items;
  final String Function(T) itemBuilder;
  final String searchHint;
  final Function(T) onItemSelected;

  const _SearchableDialog({
    required this.title,
    required this.items,
    required this.itemBuilder,
    required this.searchHint,
    required this.onItemSelected,
  });

  @override
  State<_SearchableDialog<T>> createState() => _SearchableDialogState<T>();
}

class _SearchableDialogState<T> extends State<_SearchableDialog<T>> {
  final TextEditingController _searchController = TextEditingController();
  List<T> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _filteredItems = widget.items;
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterItems);
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase().trim();
    setState(() {
      if (query.isEmpty) {
        _filteredItems = widget.items;
      } else {
        _filteredItems = widget.items.where((item) {
          final itemName = widget.itemBuilder(item).toLowerCase();
          return itemName.contains(query);
        }).toList();
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _filteredItems = widget.items;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: double.maxFinite,
        height: 500,
        child: Column(
          children: [
            // Search Field
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.search, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: widget.searchHint,
                        border: InputBorder.none,
                        hintStyle: const TextStyle(color: Colors.grey),
                      ),
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                  if (_searchController.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: _clearSearch,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                ],
              ),
            ),
            const SizedBox(height: 12),

            // Results
            Expanded(
              child: _filteredItems.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 48,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchController.text.isEmpty
                                ? 'No items available'
                                : 'No results found for "${_searchController.text}"',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (_searchController.text.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            TextButton(
                              onPressed: _clearSearch,
                              child: const Text('Clear search'),
                            ),
                          ],
                        ],
                      ),
                    )
                  : ListView.builder(
                      shrinkWrap: true,
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        final itemName = widget.itemBuilder(item);

                        return ListTile(
                          title: Text(itemName),
                          onTap: () => widget.onItemSelected(item),
                          dense: true,
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ],
    );
  }
}
