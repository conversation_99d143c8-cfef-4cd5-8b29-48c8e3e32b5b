import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/api_service.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/data/models/payment_response_model.dart';
import 'package:platix/utils/app_export.dart';

class CartScreen extends StatelessWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final CartController cartController = Get.find();

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Shopping Cart',
        centerTitle: true,
        backgroundColor: AppColors.primary,
        leadingBack: true,
        textStyle: CustomTextStyles.b4_1.copyWith(color: Colors.white),
      ),
      body: Obx(() {
        if (cartController.cartItems.isEmpty) {
          return const Center(
            child: Text('Your cart is empty.'),
          );
        }
        if (cartController.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        return Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: cartController.cartItems.length,
                itemBuilder: (context, index) {
                  final cartItem = cartController.cartItems[index];
                  return ListTile(
                    leading: CustomImageView(
                      imagePath: cartItem.item.photoUrl.isNotEmpty
                          ? cartItem.item.photoUrl.first
                          : null,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                    ),
                    title: Text(cartItem.item.name),
                    subtitle: Text(
                        'Price: \u20B9${cartItem.item.discountedPrice.toStringAsFixed(2)}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.remove),
                          onPressed: () =>
                              cartController.decrementQuantity(cartItem),
                        ),
                        Text(cartItem.quantity.toString()),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () =>
                              cartController.incrementQuantity(cartItem),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total: \u20B9${cartController.totalPrice.toStringAsFixed(2)}',
                    style: CustomTextStyles.h6,
                  ),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text('Checkout'),
                    onPressed: () async {
                      cartController.isLoading.value = true;
                      try {
                        final response =
                            await cartController.initiatePayment();
                        if (response.success) {
                          if (response.orderId != null &&
                              response.paymentSessionId != null) {
                            AppUtils.initiateCashFreePayment(
                              response.orderId!,
                              response.paymentSessionId!,
                              (String orderId) {
                                Get.offAllNamed(
                                  AppRoutes.paymentSuccessScreen,
                                  arguments: orderId,
                                );
                              },
                              (error, orderId) {
                                Get.snackbar('Error',
                                    'Payment failed: ${error.getMessage()}',
                                    backgroundColor: AppColors.primary3,
                                    colorText: Colors.white,
                                    duration: const Duration(seconds: 3),
                                    snackPosition: SnackPosition.TOP,
                                    );
                              },
                            );
                          } else {
                            Get.snackbar(
                                'Error', 'Failed to get payment details',
                                backgroundColor: AppColors.primary3,
                                colorText: Colors.white,
                                duration: const Duration(seconds: 3),
                                snackPosition: SnackPosition.TOP,
                                );
                          }
                        } else {
                          Get.snackbar(
                              'Error', 'Payment failed: ${response.message}',
                              backgroundColor: AppColors.primary3,
                              colorText: Colors.white,
                              duration: const Duration(seconds: 3),
                              snackPosition: SnackPosition.TOP,
                              );
                        }
                      } catch (e) {
                        Get.snackbar('Error', 'Payment failed: $e',
                            backgroundColor: AppColors.primary3,
                            colorText: Colors.white,
                            duration: const Duration(seconds: 3),
                            snackPosition: SnackPosition.TOP,
                            );
                      } finally {
                        cartController.isLoading.value = false;
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        );
      }),
    );
  }
}

extension CartControllerPayment on CartController {
  Future<PaymentResponse> initiatePayment() async {
    try {
      final items = cartItems.map((cartItem) {
        return {
          'distributor_id': cartItem.item.distributorId,
          'itemId': cartItem.item.id,
          'amount': cartItem.item.discountedPrice,
          'quantity': cartItem.quantity,
        };
      }).toList();

      final orderData = {
        'items': items,
        'totalAmount': totalPrice,
        'is_cart_items': true,
      };

      return await ApiService().createCashFreeOrder(orderData);
    } catch (e) {
      return PaymentResponse(success: false, message: e.toString());
    }
  }

  void clearCart() {
    cartItems.clear();
    update();
  }
}
