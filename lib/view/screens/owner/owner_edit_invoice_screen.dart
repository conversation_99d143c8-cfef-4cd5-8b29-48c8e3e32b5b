import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/owner/owner_invoice_controller.dart';
import 'package:platix/controllers/owner/owner_orders_controller.dart';
import 'package:platix/data/models/owner/owner_report_details_info.dart';
import 'package:platix/data/models/user_model.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/owner/owner_editorder_success_screen.dart';
import '../../../api/data_store.dart';
import '../../../controllers/signin_controller.dart';

class OwnerEditInvoiceScreen extends StatefulWidget {
  final String id;
  const OwnerEditInvoiceScreen({super.key, required this.id});

  @override
  State<OwnerEditInvoiceScreen> createState() => _OwnerEditInvoiceScreenState();
}

class _OwnerEditInvoiceScreenState extends State<OwnerEditInvoiceScreen> {
  OwnerInvoiceController invoiceController = Get.put(OwnerInvoiceController());
  OwnerOrdersController ordersController = Get.find();
  final SignInController signInController = Get.put(SignInController());

  UserModel? userModel;


  final _formKey = GlobalKey<FormState>();
  late OwnerReportDetails _currentOrder;

  // Text Controllers
  late TextEditingController doctorNameController;
  late TextEditingController hospitalNameController;
  late TextEditingController patientNameController;
  late TextEditingController patientIdController;
  late TextEditingController orderDateController;
  late TextEditingController requiredDateController;
  late TextEditingController radiologyNameController;
  late List<TextEditingController> serviceController;
  late TextEditingController remarkController;
  late TextEditingController toothNameController;
  late TextEditingController reasonController;
  late TextEditingController totalAmountController;
  late TextEditingController shadeController;

  List<String> items = ['White', 'Light yellow'];

  List<OwnerReportService> selectedServices = [];
  String? selectedShade;
  String? selectedToothValue;

  DateTime? orderDate;
  DateTime? requiredDate;
  bool _orderDateError = false;
  bool _requiredDateError = false;

  double totalAmount = 0.0;
  double subTotal = 0.0;
  double tax = 0.0;
  double serviceCharges = 0.0;


  String formatDateString(String? dateStr) {
    DateTime parsedDate = DateTime.parse(dateStr!);
    return DateFormat('dd-MM-yyyy').format(parsedDate);
  }

  final userRecord = UserRecord.fromJson(getData.read('userRecord'));

  void calculateTotals() {
    subTotal = selectedServices.fold(
      0.0, (sum, item) => sum + ((num.tryParse(item.orgservice?.price ?? '')) ?? 0.0),);
    tax = (subTotal * 18.0)/100;

    serviceCharges = double.tryParse(_currentOrder.serviceCharges ?? '') ?? 0.0;
    totalAmount = subTotal + tax + serviceCharges;
    setState(() {});
  }
  @override
  void initState() {
    super.initState();
    final radiologyName = userRecord.organization?.name;
    log('radiology : ${userRecord.organization?.name}');

    // Find the specific order
    _currentOrder = ordersController.ownerReportDetailsModel!.data!;

    // Initialize controllers with current order data
    doctorNameController =
        TextEditingController(text: _currentOrder.userDetails?.firstName);
    hospitalNameController =
        TextEditingController(text: _currentOrder.userDetails?.organization?.name);
    patientNameController =
        TextEditingController(text: _currentOrder.patientName);
    radiologyNameController =
        TextEditingController(text:radiologyName);
    toothNameController =
        TextEditingController(text: _currentOrder.toothName);
    shadeController = TextEditingController(text: _currentOrder.shades);
    var orderDateData = _currentOrder.orderDate;
    if (orderDateData is String)
    {
      orderDate = DateFormat("dd-MM-yyyy").parse(orderDateData.toString());
    } else if (orderDateData is DateTime) {
      orderDate = orderDateData;
    } else {
      orderDate = null;
    }

    var requiredDateData = _currentOrder.requiredDate;
    if (requiredDateData is String) {
      requiredDate = DateFormat("dd-MM-yyyy").parse(requiredDateData.toString());
    } else if (requiredDateData is DateTime) {
      requiredDate = requiredDateData;
    } else {
      requiredDate = null;
    }

    reasonController = TextEditingController(text: _currentOrder.reasonForScan );
    remarkController = TextEditingController(text:  _currentOrder.remarks);
    selectedServices = _currentOrder.orderServices ?? [];
    orderDateController = TextEditingController(text: orderDate != null ? DateFormat("dd-MM-yyyy").format(orderDate!) : "");
    requiredDateController =  TextEditingController(text: requiredDate != null ? DateFormat("dd-MM-yyyy").format(requiredDate!) : "");
    totalAmountController = TextEditingController(text: _currentOrder.subTotal);

    // Initialize service controller by joining all service names with space
    //serviceController = TextEditingController(text: _currentOrder.service.map((service) => service.name).join(' ')) as List<TextEditingController>;
  }


  void _saveChanges() async {
    calculateTotals();
    if (_formKey.currentState!.validate()) {
      // Update the order details
      setState(() {
        _currentOrder.subTotal = totalAmountController.text;
        _currentOrder.remarks = remarkController.text;
      });

      // Convert the updated order to JSON
      Map<String, dynamic> updatedData = {

        "subTotal":_currentOrder.subTotal,

        "totalAmount": double.parse(totalAmountController.text) + double.parse(_currentOrder.tax ?? '0.0'),

        "remarks":_currentOrder.remarks
      };

      try {
        bool isSuccess = await invoiceController.editInvoice(updatedData, widget.id);

        if (isSuccess) {
          // ✅ Update order status
          ordersController.fetchOrders(status: 'processing');

          // ✅ Navigate to success screen only if successful
          Get.to(()=> const OwnerEditOrderSuccessScreen(screen: 'invoice',) );

        }
      } catch (e) {
        Get.snackbar(
          "Error",
          "An unexpected error occurred: $e",
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Edit Invoice',
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Form(
            key: _formKey,
            child: Center(
              child: Container(
                color: Colors.white,
                width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppSizes.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          LabelTextField(
                            label: 'Name of Doctor:',
                            hint: 'Enter Doctor Name',
                            controller: doctorNameController,
                            //validator: AppValidators.validateText,
                            isDisabled: true,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            label: 'Name of Hospital:',
                            hint: 'Enter Hospital Name',
                            controller: hospitalNameController,
                           // validator: AppValidators.validateText,
                            isDisabled: true,
                          ),

                          if(userRecord.organization?.organizationType?.organizationType != 'Material Supplier')...[
                            const SizedBox(
                              height: AppSizes.defaultSpace,
                            ),
                            LabelTextField(
                              label: 'Patient Name:',
                              hint: 'Enter Patient Name',
                              controller: patientNameController,
                              //validator: AppValidators.validateText,
                              isDisabled: true,
                            ),
                          ],
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            label: getOrgLabel(ordersController.ownerReportDetailsModel?.toOrganizationDetails?.organizationType?.organizationType),
                            hint: 'Enter Organization Name',
                            controller: radiologyNameController,
                           // validator: AppValidators.validateText,
                            isDisabled: true,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          const TextFieldLabel(label: 'Service Name'),

                          Wrap(
                            spacing: 8.0,
                            runSpacing: 8.0,
                            children: selectedServices.map((service) {
                              return Chip(
                                label: Text(
                                  service.orgservice?.services?.servicename ?? '',
                                  style: CustomTextStyles.b4_1.copyWith(color: AppColors.black),
                                ),
                                //deleteIcon: const Icon(Icons.close, size: 18, color: AppColors.black),
                                // onDeleted: () {
                                //   if(_currentOrder.paymentStatus == 'unpaid'){
                                //     if (selectedServices.length == 1) {
                                //       // Show error message if only one service is left
                                //       ScaffoldMessenger.of(context).showSnackBar(
                                //         const SnackBar(
                                //           content: Text("You must have at least one selected service!"),
                                //           backgroundColor: Colors.red,
                                //           duration: Duration(seconds: 2),
                                //         ),
                                //       );
                                //     } else {
                                //       setState(() {
                                //         log("Removed Service ID : ${service.orgservice?.id}");
                                //         log("Removed Service Name : ${service.orgservice?.services?.servicename}");
                                //         selectedServices.remove(service);
                                //         calculateTotals();
                                //       });
                                //     }
                                //   }
                                // },
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                backgroundColor: Colors.purple.withOpacity(0.2),
                              );
                            }).toList(),
                          ),

                          const SizedBox(
                            height: AppSizes.spaceBtwItems,
                          ),

                          LabelTextField(
                            label: 'Price',
                            hint: 'Enter Amount',
                            controller: totalAmountController,
                            validator: AppValidators.validateText,
                          ),


                          if(userRecord.organization?.organizationType?.organizationType == 'Radiology')...[
                            const SizedBox(height: AppSizes.defaultSpace),
                            LabelTextField(
                              maxLines: 3,
                              label: 'Reason for scan',
                              hint: 'Write Note..',
                              controller: reasonController,
                              isMandatory: true,
                              isDisabled: true,
                            ),
                          ],
                          const SizedBox(height: AppSizes.defaultSpace),

                          _buildDateField(orderDate, (date) {
                            setState(() {
                              orderDate = date;
                              _orderDateError = false;
                            });
                          }, 'Order Date', _orderDateError,isDisabled: true),

                          const SizedBox(height: AppSizes.defaultSpace),
                          _buildDateField(requiredDate, (date) {
                            requiredDate = date;
                            _requiredDateError = false;
                          }, 'Required Date', _requiredDateError, isDisabled: true),


                          if(userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory')...[
                            const SizedBox(
                              height: AppSizes.defaultSpace,
                            ),

                            LabelTextField(
                              label: 'Tooth Name',
                              hint: 'Select value',
                              controller: toothNameController,
                              isMandatory: true,
                              isDisabled: true,
                            ),
                            const SizedBox(
                              height: AppSizes.defaultSpace,
                            ),

                            LabelTextField(
                              label: 'Shade',
                              hint: 'Select Shade',
                              controller: shadeController,
                              isMandatory: true,
                              isDisabled: true,
                            ),
                          ],

                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            maxLines: 3,
                            label: 'Remarks',
                            hint: 'Write Note..',
                            controller: remarkController,
                            isMandatory: false,
                          ),
                          const SizedBox(height: AppSizes.defaultSpace),
                          CustomElevatedButton(
                            text: 'Update',
                            onPressed: _saveChanges,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            )),
      ),
    );
  }

  Widget _buildDateField(
      DateTime? date,
      Function(DateTime) onDateSelected,
      String label,
      bool showError, {
        bool isDisabled = false,
      }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: isDisabled
              ? null
              : () => _selectDate(context, onDateSelected),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width > 600
                ? Get.size.width * 0.5
                : double.infinity,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: isDisabled ? Colors.grey.shade300 : Colors.white,
              borderRadius: BorderRadiusStyle.border12,
              border: Border.all(color: AppColors.darkGrey, width: 1)
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null
                      ? DateFormat('dd-MM-yyyy').format(date)
                      : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(
                      color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2
                      .copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }

  Future<void> _selectDate(
      BuildContext context, Function(DateTime) onDateSelected) async {
    DateTime initialDate = DateTime.now();
    if (orderDateController.text.isNotEmpty) {
      // If a date is already set, parse it
      List<String> dateParts = orderDateController.text.split('-');
      initialDate = DateTime(int.parse(dateParts[2]), int.parse(dateParts[1]),
          int.parse(dateParts[0]));
    }

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  String getOrgLabel(String? orgName) {
    // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supplier";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}