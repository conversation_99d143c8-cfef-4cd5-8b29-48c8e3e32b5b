import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/owner/owner_create_order_controller.dart';
import 'package:platix/controllers/owner/owner_orders_controller.dart';
import 'package:platix/data/models/owner/owner_report_details_info.dart';
import 'package:platix/data/models/user_model.dart' as user_model;
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/owner/owner_editorder_success_screen.dart';
import '../../../api/data_store.dart';
import '../../../controllers/signin_controller.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';

class OwnerEditOrderScreen extends StatefulWidget {
  final String id;

  const OwnerEditOrderScreen({super.key, required this.id});

  @override
  State<OwnerEditOrderScreen> createState() => _OwnerEditOrderScreenState();
}

class _OwnerEditOrderScreenState extends State<OwnerEditOrderScreen> {
  OwnerCreateOrderController editController =
      Get.put(OwnerCreateOrderController());
  OwnerOrdersController ordersController = Get.find();
  final SignInController signInController = Get.find();

  user_model.UserModel? userModel;

  final _formKey = GlobalKey<FormState>();
  late OwnerReportDetails _currentOrder;

  // Text Controllers
  late TextEditingController doctorNameController;
  late TextEditingController hospitalNameController;
  late TextEditingController patientNameController;
  late TextEditingController patientIdController;
  late TextEditingController orderDateController;
  late TextEditingController requiredDateController;
  late TextEditingController radiologyNameController;
  late List<TextEditingController> serviceController;
  late TextEditingController remarkController;
  late TextEditingController toothNameController;
  late TextEditingController reasonController;
  String? techAssignmentStatus;
  String? delAssignmentStatus;


  List<OwnerReportService> selectedServices = [];
  List<Choice<user_model.OrganizationService>> selectedServices1 = [];
  String? selectedShade;
  String? selectedToothValue;

  DateTime? orderDate;
  DateTime? requiredDate;
  bool _orderDateError = false;
  bool _requiredDateError = false;
  bool showServicesError = false;

  double totalAmount = 0.0;
  double subTotal = 0.0;
  double tax = 0.0;
  double serviceCharges = 0.0;

  String formatDateString(String? dateStr) {
    DateTime parsedDate = DateTime.parse(dateStr!);
    return DateFormat('dd-MM-yyyy').format(parsedDate);
  }

  final userRecord = user_model.UserRecord.fromJson(getData.read('userRecord'));

  void calculateTotals() {
    subTotal = selectedServices1.fold(
      0.0,
      (sum, item) =>
          sum + ((num.tryParse(item.metadata?.price.toString() ?? '')) ?? 0.0),
    );
    tax = (subTotal * 18.0) / 100;

    serviceCharges = double.tryParse(_currentOrder.serviceCharges ?? '') ?? 0.0;
    totalAmount = subTotal + tax + serviceCharges;
    setState(() {});
  }

  @override
  void initState() {

    super.initState();
    final radiologyName = userRecord.organization?.name;
    log('radiology : ${userRecord.organization?.name}');

    // Find the specific order
    _currentOrder = ordersController.ownerReportDetailsModel!.data!;

    // Initialize controllers with current order data
    doctorNameController =
        TextEditingController(text: _currentOrder.userDetails?.firstName);
    hospitalNameController =
        TextEditingController(text: _currentOrder.userDetails?.organization?.name );
    patientNameController =
        TextEditingController(text: _currentOrder.patientName);
    radiologyNameController = TextEditingController(text: radiologyName);
    toothNameController = TextEditingController(text: _currentOrder.toothName);

    techAssignmentStatus=_currentOrder.techAssignmentStatus;
    delAssignmentStatus=_currentOrder.delAssignmentStatus;
    var orderDateData = _currentOrder.orderDate;
    if (orderDateData is String) {
      orderDate = DateFormat("dd-MM-yyyy").parse(orderDateData.toString());
    } else if (orderDateData is DateTime) {
      orderDate = orderDateData;
    } else {
      orderDate = null;
    }

    var requiredDateData = _currentOrder.requiredDate;
    if (requiredDateData is String) {
      requiredDate =
          DateFormat("dd-MM-yyyy").parse(requiredDateData.toString());
    } else if (requiredDateData is DateTime) {
      requiredDate = requiredDateData;
    } else {
      requiredDate = null;
    }

    reasonController = TextEditingController(text: _currentOrder.reasonForScan);
    remarkController = TextEditingController(text: _currentOrder.remarks);
    selectedShade = _currentOrder.shades;
    selectedServices = _currentOrder.orderServices ?? [];
    selectedToothValue = _currentOrder.toothName;

    orderDateController = TextEditingController(
        text: orderDate != null
            ? DateFormat("dd-MM-yyyy").format(orderDate!)
            : "");
    requiredDateController = TextEditingController(
        text: requiredDate != null
            ? DateFormat("dd-MM-yyyy").format(requiredDate!)
            : "");
    // Initialize service controller by joining all service names with space
    //serviceController = TextEditingController(text: _currentOrder.service.map((service) => service.name).join(' ')) as List<TextEditingController>;
      //editController.selectedServices.clear();
      initializeServices(selectedServices);

  }

  void initializeServices(List<OwnerReportService> services){
    selectedServices1 = services.map((orgService) => Choice(orgService.orgservice?.id, orgService.orgservice?.services?.servicename ?? '', metadata: user_model.OrganizationService(id: orgService.orgservice?.id,servicess: user_model.Services(id: orgService.orgservice?.id, servicename: orgService.orgservice?.services?.servicename, servicedescription: orgService.orgservice?.services?.servicedescription),price: orgService.orgservice?.price))).toList();
    setState(() {

    });
  }

  void _saveChanges() async {
    calculateTotals();
    if (_formKey.currentState!.validate()) {
      // Update the order details
      setState(() {
        showServicesError = selectedServices1.isEmpty;
        _currentOrder.userDetails?.firstName = doctorNameController.text;
        _currentOrder.patientName = patientNameController.text;
        _currentOrder.toothName = selectedToothValue;
        _currentOrder.orderDate = orderDate;
        _currentOrder.requiredDate = requiredDate;
        _currentOrder.reasonForScan = reasonController.text;
        _currentOrder.remarks = remarkController.text;
      });

      // Convert the updated order to JSON
      Map<String, dynamic> updatedData = {
        "id": widget.id,
        "fromOrganization": _currentOrder.fromOrganization != ''
            ? _currentOrder.fromOrganization
            : null,
        "patientName": patientNameController.text,
        "patientId": _currentOrder.patientId,
        "orderDate": _currentOrder.orderDate?.toIso8601String(),
        "delivery_boy": _currentOrder.deliveryBoy,
        "userUUID": _currentOrder.userUUID,
        "toOrganization": _currentOrder.toOrganization != ''
            ? _currentOrder.toOrganization
            : null,
        "serviceId": selectedServices1
            .map((service) => {
                  "id": service.metadata?.id ?? '',
                  "quantity": selectedServices.any((previousService) =>
                          previousService.orgservice?.id ==
                          service.metadata?.id)
                      ? selectedServices
                          .firstWhere((previousService) =>
                              previousService.orgservice?.id ==
                              service.metadata?.id)
                          .quantity
                      : 1,
                })
            .toList(),
        "requiredDate": _currentOrder.requiredDate?.toIso8601String(),
        "toothName": selectedToothValue,
        "shades": selectedShade,
        "remarks": remarkController.text,
        "reasonForScan": reasonController.text,
        "sub_total": subTotal,
        "tax": tax,
        "service_charges": _currentOrder.serviceCharges,
        "paid_amount": _currentOrder.paidAmount,
        "total_amount": totalAmount,
        "payment_method": _currentOrder.paymentMethod,
        "order_status": _currentOrder.orderStatus,
        "address": _currentOrder.address
      };

      try {
        bool isSuccess = await editController.createOrder(updatedData);

        if (isSuccess) {
          // ✅ Update order status
          ordersController.fetchOrders(status: 'processing');

          // ✅ Navigate to success screen only if successful
          Get.to(() => const OwnerEditOrderSuccessScreen(
                screen: 'order',
              ));
        }
      } catch (e) {
        Get.snackbar(
          "Error",
          "An unexpected error occurred: $e",
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Edit Order',
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Form(
            key: _formKey,
            child: Center(
              child: Container(
                color: Colors.white,
                width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppSizes.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          LabelTextField(
                            isDisabled: true,
                            label: 'Name of Doctor:',
                            hint: 'Enter Doctor Name',
                            controller: doctorNameController,
                            validator: AppValidators.validateText,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            isDisabled: true,
                            label: 'Name of Hospital:',
                            hint: 'Enter Hospital Name',
                            controller: hospitalNameController,
                            validator: AppValidators.validateText,
                          ),

                          if (userRecord.organization?.organizationType
                                  ?.organizationType !=
                              'Material Supplier') ...[
                            const SizedBox(
                              height: AppSizes.defaultSpace,
                            ),
                            LabelTextField(
                              label: 'Patient Name:',
                              hint: 'Enter Patient Name',
                              controller: patientNameController,
                              validator: AppValidators.validateText,
                            ),
                          ],
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            label: getOrgLabel(ordersController
                                .ownerReportDetailsModel
                                ?.toOrganizationDetails
                                ?.organizationType
                                ?.organizationType),
                            hint: 'Enter Organization Name',
                            controller: radiologyNameController,
                            validator: AppValidators.validateText,
                            isDisabled: true,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          TextFieldLabel(
                              label: userRecord.organization?.organizationType
                                          ?.organizationType ==
                                      'Material Supplier'
                                  ? 'Materials'
                                  : 'Service Name'),
                          if ((_currentOrder.techAssignmentStatus?.toLowerCase() == 'unassigned' && _currentOrder.delAssignmentStatus?.toLowerCase() == 'unassigned') && _currentOrder.paymentStatus == 'unpaid') ...[
                            GetBuilder<OwnerCreateOrderController>(
                                builder: (controller) {
                              return Column(
                                children: [
                                  MultiSelectField<user_model.OrganizationService>(
                                    label: "Select Services",
                                    textStyleLabel: CustomTextStyles.b4_1
                                        .copyWith(color: AppColors.darkGrey),
                                    data: () {
                                      final services = userRecord
                                          .organization?.organizationService;

                                      // log("Dropdown services: ${services?.length}");
                                      return services
                                              ?.map<Choice<user_model.OrganizationService>>(
                                                  (service) =>
                                                      Choice<user_model.OrganizationService>(
                                                        service.id.toString(),
                                                        service.servicess
                                                                ?.servicename ??
                                                            '',
                                                        metadata: service,
                                                      ))
                                              .toList() ??
                                          [];
                                    },
                                    onSelect: (List<Choice<user_model.OrganizationService?>>
                                            selectedChoices,
                                        bool isFromDefaultData) {
                                      selectedServices1.assignAll(
                                          selectedChoices
                                              .cast<Choice<user_model.OrganizationService>>());

                                      showServicesError = false;
                                      calculateTotals();
                                    },
                                    defaultData: selectedServices1,
                                    cleanCurrentSelection:
                                        editController.cleanCurrentSelection1.value,
                                    singleSelection: false,
                                    useTextFilter: true,
                                    menuStyle: MenuStyle(
                                      shape: WidgetStatePropertyAll(
                                          RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8))),
                                      backgroundColor: const WidgetStatePropertyAll(
                                          Colors.white),
                                      maximumSize: WidgetStatePropertyAll<Size>(
                                        Size(Get.size.width,
                                            200), // Set width and height manually
                                      ),
                                    ),
                                    itemColor: ItemColor(
                                        selected: Colors.purple.withOpacity(0.2),
                                        unSelected: Colors.white),
                                    itemMenuStyle: CustomTextStyles.b4_1,
                                    isDisabled: false,
                                  ),
                                  if (showServicesError)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: AppSizes.sm, left: AppSizes.md),
                                      child: Text(
                                        "Please Select Atleast One Service",
                                        style: CustomTextStyles.b6.copyWith(
                                          color: Colors.redAccent,
                                        ),
                                      ),
                                    )
                                ],
                              );


                            }),
                          ] else ...[
                            Wrap(
                              spacing: 8.0,
                              runSpacing: 8.0,
                              children: selectedServices.map((service) {
                                return Chip(
                                  label: Text(
                                    service.orgservice?.services?.servicename ??
                                        '',
                                    style: CustomTextStyles.b4_1
                                        .copyWith(color: AppColors.black),
                                  ),
                                  deleteIcon: ((_currentOrder.paymentStatus == 'unpaid' && (_currentOrder.techAssignmentStatus?.toLowerCase() == 'unassigned' && _currentOrder.delAssignmentStatus?.toLowerCase() == 'unassigned') ) && selectedServices.length > 1) ? const Icon(Icons.close, size: 18, color: AppColors.black) : const SizedBox(),
                                  onDeleted: () {
                                    if (_currentOrder.paymentStatus ==
                                        'unpaid' && (_currentOrder.techAssignmentStatus?.toLowerCase() == 'unassigned' && _currentOrder.delAssignmentStatus?.toLowerCase() == 'unassigned') ) {
                                      if (selectedServices.length == 1) {
                                        // Show error message if only one service is left
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                                "You must have at least one selected service!"),
                                            backgroundColor: Colors.red,
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      } else {
                                        setState(() {
                                          log("Removed Service ID : ${service.orgservice?.id}");
                                          log("Removed Service Name : ${service.orgservice?.services?.servicename}");
                                          selectedServices.remove(service);
                                          calculateTotals();
                                        });
                                      }
                                    }
                                  },
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  backgroundColor:
                                      Colors.purple.withOpacity(0.2),
                                );
                              }).toList(),
                            ),
                          ],

                          if (userRecord.organization?.organizationType
                                  ?.organizationType ==
                              'Radiology') ...[
                            const SizedBox(height: AppSizes.defaultSpace),
                            LabelTextField(
                              maxLines: 3,
                              label: 'Reason for scan',
                              hint: 'Write Note..',
                              controller: reasonController,
                              isMandatory: true,
                            ),
                          ],
                          const SizedBox(height: AppSizes.defaultSpace),

                          _buildDateField(orderDate, (date) {
                            setState(() {
                              orderDate = date;
                              _orderDateError = false;
                            });
                          }, 'Order Date', _orderDateError, isDisabled: true),

                          const SizedBox(height: AppSizes.defaultSpace),

                          _buildDateField(
                            requiredDate,
                                (date) {
                              requiredDate = date;
                              _requiredDateError = false;
                            },
                            userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory'
                                ? 'Delivery Date'
                                : 'Required Date',
                            _requiredDateError,
                            isDisabled: techAssignmentStatus == "technician_completed" || delAssignmentStatus == "assigned_to_delivery_boy",
                          ),

                          if (userRecord.organization?.organizationType
                                  ?.organizationType ==
                              'Dental Laboratory') ...[
                            const SizedBox(
                              height: AppSizes.defaultSpace,
                            ),
                            const TextFieldLabel(label: 'Tooth Name'),
                            CustomDropdown(
                              dropdownWidth:
                                  MediaQuery.of(context).size.width > 600
                                      ? Get.size.width * 0.5
                                      : Get.size.width - 30,
                              hintText: 'Select Tooth ',
                              items: const [
                                "Full Mouth",
                                '11',
                                '12',
                                '13',
                                '14',
                                '15',
                                '16',
                                '17',
                                '18',
                                '21',
                                '22',
                                '23',
                                '24',
                                '25',
                                '26',
                                '27',
                                '28',
                                '31',
                                '32',
                                '33',
                                '34',
                                '35',
                                '36',
                                '37',
                                '38',
                                '41',
                                '42',
                                '43',
                                '44',
                                '45',
                                '46',
                                '47',
                                '48'
                              ],
                              // Custom tooth numbers
                              onChanged: (value) {
                                setState(() {
                                  selectedToothValue = value;
                                });
                              },
                              selectedValue: selectedToothValue,
                            ),
                            const SizedBox(height: AppSizes.defaultSpace),
                            const TextFieldLabel(label: 'Shade'),
                            CustomDropdown(
                              hintText: 'Select',
                              items: const ['Light', 'Medium', 'Dark'],
                              selectedValue: selectedShade,
                              onChanged: (value) {
                                selectedShade = value;
                              },
                            ),
                          ],

                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            maxLines: 3,
                            label: 'Remarks',
                            hint: 'Write Note..',
                            controller: remarkController,
                            isMandatory: false,
                          ),
                          const SizedBox(height: AppSizes.defaultSpace),
                          CustomElevatedButton(
                            text: 'Update',
                            onPressed: _saveChanges,
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            )),
      ),
    );
  }

  Widget _buildDateField(
    DateTime? date,
    Function(DateTime) onDateSelected,
    String label,
    bool showError, {
    bool isDisabled = false,
  })
  {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: isDisabled
              ? null
              : () => _selectDate(context, date, onDateSelected),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width > 600
                ? Get.size.width * 0.5
                : double.infinity,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: isDisabled ? AppColors.lightGrey : Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null
                      ? DateFormat('dd-MM-yyyy').format(date)
                      : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(
                          color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2
                          .copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }

  Future<void> _selectDate(
    BuildContext context,
    DateTime? currentValue,
    Function(DateTime) onDateSelected,
  ) async {
    final DateTime now = DateTime.now();

    final DateTime initialDate = currentValue ?? now;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );


    if (picked != null) {
      onDateSelected(picked);
    }
  }

  String getOrgLabel(String? orgName) {
    // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supplier";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}
