import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/data/models/owner/owner_report_details_info.dart';
import 'package:platix/utils/app_export.dart';
import '../../../api/data_store.dart';
import '../../../controllers/owner/owner_orders_controller.dart';
import '../../../controllers/signin_controller.dart';
import '../../../data/models/user_model.dart';

class OwnerPayNowOrderDetails extends StatefulWidget {
  final String id;
  final String type;

  const OwnerPayNowOrderDetails(
      {super.key, required this.id, required this.type});

  @override
  State<OwnerPayNowOrderDetails> createState() =>
      _OwnerPayNowOrderDetailsState();
}

class _OwnerPayNowOrderDetailsState extends State<OwnerPayNowOrderDetails> {
  final OwnerOrdersController orderController = Get.find();
  final SignInController signInController = Get.put(SignInController());
  late TextEditingController technician = TextEditingController();
  List<OwnerReportService> service = [];

  String? path;

  final userRecord = UserRecord.fromJson(getData.read('userRecord'));

  @override
  void initState() {
    super.initState();
    initialize();
  }

  String formatOrderDate(String? rawDate) {
    if (rawDate == null || rawDate.isEmpty) return 'Invalid date';
    try {
      final dateTime = DateTime.parse(rawDate);
      return DateFormat('dd-MM-yyyy').format(dateTime);
    } catch (_) {
      return 'Invalid date';
    }
  }

  Future<void> initialize() async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      orderController.getOrderDetails(id: widget.id, type: widget.type).then((_) {
        if (mounted) {
          setState(() {
            service = orderController.ownerReportDetailsModel?.data?.orderServices ?? [];
            orderController.fetchTechnicians();

            final technicianId = orderController.ownerReportDetailsModel?.data?.technician;
            final matchedTechnician = orderController.technicianList.firstWhere(
                  (tech) => tech.id == technicianId,
            );

            technician = TextEditingController(
              text: matchedTechnician != null
                  ? "${matchedTechnician.firstName} ${matchedTechnician.lastName}"
                  : '',
            );
            log("technician :$technician");
          });
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final order = orderController.ownerReportDetailsModel?.data;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Order Details',
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Center(
          child: Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: AppSizes.defaultSpace),
                  // Order details and edit order
                  Row(
                    children: [
                      Text('Order Details',
                          style: CustomTextStyles.b4
                              .copyWith(fontWeight: FontWeight.w700)),
                      const Spacer(),
                    ],
                  ),
                  const SizedBox(height: AppSizes.spaceSmall),
                  // Order details container
                  Container(
                    padding: const EdgeInsets.all(AppSizes.md),

                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.16),
                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                    ),
                    child: Column(
                      children: [
                        _buildRow(
                            'Order Id', order?.orderId ?? '', CustomTextStyles.b5),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Doctor Name', order?.userDetails?.firstName ?? '', CustomTextStyles.b5),
                        // const SizedBox(height: AppSizes.spaceSmall),
                        // _buildRow('Hospital Name', order?.userDetails?.hospitalName ?? '',CustomTextStyles.b5),
                      if(userRecord.organization?.organizationType?.organizationType != 'Material Supplier'  &&
                          ((order?.patientName?.trim().isNotEmpty ?? false) && (order?.patientId?.trim().isNotEmpty ?? false)))...[
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Patient Name', order?.patientName ?? '', CustomTextStyles.b5),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Patient Id', order?.patientId ?? '',
                            CustomTextStyles.b5),
                        ],
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow(getOrgLabel(userRecord.organization?.organizationType?.organizationType), userRecord.organization?.name ?? '', CustomTextStyles.b5),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Payment Status', order?.paymentStatus ?? '', CustomTextStyles.b5.copyWith(color: AppColors.darkGreen)),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Order Date', formatOrderDate(order?.orderDate.toString()) , CustomTextStyles.b5),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow(userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory' ? 'Delivery Date' : 'Required Date', formatOrderDate(order?.requiredDate.toString()), CustomTextStyles.b5),
                      ],
                    ),
                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  // Services section
                  const SizedBox(height: AppSizes.spaceSmall),
                  _orderList(service),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  const SizedBox(
                    height: AppSizes.spaceSmall,
                  ),

                  Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text("Assigned to",
                                style: CustomTextStyles.b4
                                    .copyWith(fontWeight: FontWeight.w700)),
                            const SizedBox(
                              height: AppSizes.spaceSmall,
                            ),
                            CustomTextFormField(
                              width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width > 1000) ?  Get.size.width * 0.8  : double.infinity,
                              controller: technician,
                              allowShadow: true,
                              hintStyle:
                                  const TextStyle(color: AppColors.darkerGrey),
                              hintText: 'Technician',
                              filled: true,
                              fillColor: AppColors.grey,
                              enabled: false,
                              borderDecoration: OutlineInputBorder(
                                  borderSide: BorderSide.none,
                                  borderRadius: BorderRadiusStyle.border12),
                            ),
                          ],
                        ),

                  /// uncomment it for uploading images
                  //  const SizedBox(
                  //    height: AppSizes.spaceBtwItems,
                  //  ),
                  //
                  //  MediaQuery.of(context).size.width>750
                  //  ?Center(
                  //    child: Column(
                  //      crossAxisAlignment: CrossAxisAlignment.start,
                  //      children: [
                  //        Text(
                  //            'Upload Images (max 4)',
                  //            style: CustomTextStyles.b4.copyWith(fontWeight: FontWeight.w700)
                  //        ),
                  //        const SizedBox(
                  //          height: AppSizes.spaceSmall,
                  //        ),
                  //
                  //                     CustomTextFormField(
                  //         hintText: 'No file choosen',
                  //         suffix: Padding(
                  //           padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                  //           child: CustomElevatedButton(
                  //               height: 34,
                  //               width:100,
                  //               text: 'Choose',
                  //               onPressed: _pickImages,
                  //               buttonStyle: ButtonStyle(
                  //                   shape:WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)) ,
                  //                   padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))
                  //               ),
                  //           ),
                  //         ),
                  //                     ),
                  //      ],
                  //    ),
                  //  )
                  //  :Column(
                  //    crossAxisAlignment: CrossAxisAlignment.start,
                  //    children: [
                  //      Text(
                  //          'Upload Images (max 4)',
                  //          style: CustomTextStyles.b4.copyWith(fontWeight: FontWeight.w700)
                  //      ),
                  //      const SizedBox(
                  //        height: AppSizes.spaceSmall,
                  //      ),
                  //
                  //                   CustomTextFormField(
                  //       hintText: 'No file choosen',
                  //       suffix: Padding(
                  //         padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                  //         child: CustomElevatedButton(
                  //             height: 34,
                  //             width:100,
                  //             text: 'Choose',
                  //             onPressed: _pickImages,
                  //             buttonStyle: ButtonStyle(
                  //                 shape:WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)) ,
                  //                 padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))
                  //             ),
                  //         ),
                  //       ),
                  //                   ),
                  //    ],
                  //  ),
                  //
                  //  const SizedBox(height: AppSizes.spaceSmall,),
                  //  MediaQuery.of(context).size.width > 750
                  //  ?Center(
                  //    child: SizedBox(
                  //      width: MediaQuery.of(context).size.width > 750 ? 490 : double.infinity,
                  //      child: Container(
                  //
                  //        decoration: BoxDecoration(
                  //          color: Colors.white,
                  //          boxShadow: AppDecoration.shadow1_3,
                  //          borderRadius: BorderRadiusStyle.radius8
                  //      ),
                  //        child: ListView.separated(
                  //          shrinkWrap: true,
                  //          itemCount: _selectedImages.length,
                  //          separatorBuilder: (context, index) => Divider(thickness: 1, color: Colors.grey[300]),
                  //          itemBuilder: (context, index) {
                  //            return ListTile(
                  //              shape: RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8),
                  //              title: Text(_selectedImages[index].name, style: CustomTextStyles.b5.copyWith(color: AppColors.darkGrey),), // Display only filename
                  //              trailing: IconButton(
                  //                icon: CustomImageView(
                  //                  imagePath: AppIcons.clear,
                  //                  height: 24,
                  //                  width: 24,
                  //                ),
                  //                onPressed: () => _removeImage(index),
                  //              ),
                  //            );
                  //          },
                  //        ),
                  //      ),
                  //    ),
                  //  )
                  //  :Container(
                  //    decoration: BoxDecoration(
                  //      color: Colors.white,
                  //      boxShadow: AppDecoration.shadow1_3,
                  //      borderRadius: BorderRadiusStyle.radius8
                  //  ),
                  //    child: ListView.separated(
                  //      shrinkWrap: true,
                  //      itemCount: _selectedImages.length,
                  //      separatorBuilder: (context, index) => Divider(thickness: 1, color: Colors.grey[300]),
                  //      itemBuilder: (context, index) {
                  //        return ListTile(
                  //          shape: RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8),
                  //          title: Text(_selectedImages[index].name, style: CustomTextStyles.b5.copyWith(color: AppColors.darkGrey),), // Display only filename
                  //          trailing: IconButton(
                  //            icon: CustomImageView(
                  //              imagePath: AppIcons.clear,
                  //              height: 24,
                  //              width: 24,
                  //            ),
                  //            onPressed: () => _removeImage(index),
                  //          ),
                  //        );
                  //      },
                  //    ),
                  //  ),

                  const SizedBox(
                    height: AppSizes.defaultSpace,
                  ),
                  if (userRecord.organization?.organizationType?.organizationType ==
                          'Radiology' &&
                      order?.paymentStatus == "unpaid")
                    orderController.cashFreeLoading
                        ? const Center(
                            child: CircularProgressIndicator(),
                          )
                        : CustomElevatedButton(
                            height: 56,
                            width: Get.size.width,
                            text: 'Pay now',
                            onPressed: () async {
                              try {
                                setState(() {
                                  orderController.cashFreeLoading = true;
                                });

                                // final orderData = await orderController
                                //     .createCashFreeOrder({
                                //
                                //   // 'amount': orderController
                                //   //     .ownerReportDetailsModel!.data!.totalAmount
                                //
                                //   'amount': orderController.ownerReportDetailsModel!.data!.subTotal! +
                                //       orderController.ownerReportDetailsModel!.data!.tax.toString()
                                //
                                // });

                                final double subTotal = double.parse(orderController.ownerReportDetailsModel!.data!.subTotal!);
                                final double tax = double.parse(orderController.ownerReportDetailsModel!.data!.tax!);

                               final totalAmount = (subTotal + tax);
                               print("sdsdsdsdsdsdsdsdsd");
                               print(totalAmount);
                               //  final totalAmount = 33000;



                                final orderData = await orderController.createCashFreeOrder({
                                  'amount': totalAmount.toStringAsFixed(2),
                                  'orderData': orderController.ownerReportDetailsModel!.data,
                                  'isQRCode': true,
                                });

                                print("ddddddddddddddddddddddddddddd");
                                print(orderData);

                                if (orderData != null &&
                                    orderData.containsKey('orderId') &&
                                    orderData.containsKey('sessionId')) {
                                  String? orderId = orderData['orderId'];
                                  String? paymentSessionId = orderData['sessionId'];
                                  // String amount = orderController
                                  //     .ownerReportDetailsModel!.data!.totalAmount
                                  //     .toString();

                                  if (orderId != null && paymentSessionId != null) {
                                    AppUtils.initiateCashFreePayment(
                                        orderId, paymentSessionId,
                                        (String orderId) async {
                                      log("hhhhhhhhhhhhh");
                                      log(orderId);
                                      // ✅ Call Payment API after successful payment
                                      await orderController.makePayment(
                                          orderId: orderController
                                                  .ownerReportDetailsModel!
                                                  .data!
                                                  .id ??
                                              '',
                                          transactionId: orderId,
                                          amount: totalAmount.toStringAsFixed(2));

                                      setState(() {
                                        orderController.cashFreeLoading = false;
                                      });
                                    }, (CFErrorResponse errorResponse,
                                            String orderId) {
                                      Get.snackbar('Error',
                                          errorResponse.getMessage().toString(),
                                          snackPosition: SnackPosition.TOP,
                                          backgroundColor: AppColors.primary3,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                        );
                                      setState(() {
                                        orderController.cashFreeLoading = false;
                                      });
                                    });
                                  } else {
                                    log("Error: orderId or paymentSessionId is null");
                                    setState(() {
                                      orderController.cashFreeLoading = false;
                                    });
                                  }
                                } else {
                                  log("Error: orderData is null or missing keys");
                                  setState(() {
                                    orderController.cashFreeLoading = false;
                                  });
                                }
                              } catch (e) {
                                log("Exception: $e");
                                Get.snackbar(
                                  "Error",
                                  "An unexpected error occurred: $e",
                                  snackPosition: SnackPosition.TOP,
                                  backgroundColor: AppColors.primary3,
                                  colorText: Colors.white,
                                  duration: const Duration(seconds: 3),
                                );
                                setState(() {
                                  orderController.cashFreeLoading = false;
                                });
                              }
                            },
                          ),

                  const SizedBox(
                    height: AppSizes.defaultSpace,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRow(String label, String value, TextStyle style) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 4,
          child: Text(label, style: CustomTextStyles.b4_1),
        ),
        Text(': ', style: CustomTextStyles.b5),
        Expanded(
          flex: 6,
          child: Text(value, style: style),
        ),
      ],
    );
  }

  // Widget _orderList(List<OwnerService>? services) {
  //   // Ensure services is not null or empty
  //   if (services == null || services.isEmpty) {
  //     return const Center(
  //       child: Text("No services available."),
  //     );
  //   }
  //
  //   return Container(
  //     decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm), boxShadow: [
  //       BoxShadow(
  //         blurRadius: 4,
  //         color: AppColors.black.withOpacity(0.16),
  //       )
  //     ]),
  //
  //     child: Column(
  //       children: List.generate(services.length, (index) {
  //         final service = services[index];
  //
  //         return SizedBox(
  //           height: 56,
  //           child: Column(
  //             children: [
  //               const SizedBox(height: 10),
  //               Padding(
  //                 padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
  //                 child: Row(
  //                   children: [
  //                     Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children: [
  //                         Text(service.name, style: CustomTextStyles.b6_3),
  //                         const SizedBox(height: AppSizes.spaceExtraSmall),
  //                         Text("₹${service.price}", style: CustomTextStyles.b6_3),
  //                       ],
  //                     ),
  //                     const Spacer(),
  //                     Text('Qty', style: CustomTextStyles.b6_3),
  //                     Text(': ', style: CustomTextStyles.b6_3),
  //                     Text('${service.quantity}', style: CustomTextStyles.b6_3),
  //                   ],
  //                 ),
  //               ),
  //               const SizedBox(height: 11),
  //               if(index != services.length-1)
  //                 Container(
  //                   height: 1,
  //                   color: AppColors.grey,
  //                 ),
  //             ],
  //           ),
  //         );
  //       }),
  //     ),
  //   );
  // }
  Widget _orderList(List<OwnerReportService>? services) {
    // Ensure services is not null or empty
    if (services == null || services.isEmpty) {
      return const Center(
        child: Text("No services available."),
      );
    }

    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFieldLabel(
            label: 'Service Details',
            isMandatory: false,
            style: CustomTextStyles.b4,
          ),

          //const SizedBox(height: 8),

          Container(
            width:
                MediaQuery.of(context).size.width > 750 ? 490 : double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
              boxShadow: [
                BoxShadow(
                  blurRadius: 4,
                  color: AppColors.black.withOpacity(0.16),
                )
              ],
            ),
            child: Column(
              children: List.generate(services.length, (index) {
                final service = services[index];

                return SizedBox(
                  height: 56,
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      Padding(
                        padding:
                            const EdgeInsets.symmetric(horizontal: AppSizes.md),
                        child: Row(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                    service.orgservice?.services?.servicename ??
                                        '',
                                    style: CustomTextStyles.b6_3),
                                const SizedBox(
                                    height: AppSizes.spaceExtraSmall),
                                Text("₹${service.orgservice?.price}",
                                    style: CustomTextStyles.b6_3),
                              ],
                            ),
                            const Spacer(),
                            Text('Qty', style: CustomTextStyles.b6_3),
                            Text(': ', style: CustomTextStyles.b6_3),
                            Text('${service.quantity}',
                                style: CustomTextStyles.b6_3),
                          ],
                        ),
                      ),
                      const SizedBox(height: 11),
                      if (index != services.length - 1)
                        Container(
                          height: 1,
                          color: AppColors.grey,
                        ),
                    ],
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  String getOrgLabel(String? orgName) {
    // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supplier";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}
