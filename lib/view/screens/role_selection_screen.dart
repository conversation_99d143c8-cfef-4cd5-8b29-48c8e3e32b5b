import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import '../../api/data_store.dart';
import '../../controllers/roleselection_controller.dart';
import '../widgets/custom_dropdown1.dart';
import '../widgets/custom_radio_button.dart';

class RoleSelection extends StatefulWidget {
  const RoleSelection({super.key});

  @override
  State<RoleSelection> createState() => _RoleSelectionState();
}

class _RoleSelectionState extends State<RoleSelection> {
  RoleSelectionController controller = Get.put(RoleSelectionController());

  final TextEditingController firstName = TextEditingController();
  final TextEditingController lastName = TextEditingController();
  final TextEditingController email = TextEditingController();

  bool showErrorMessage= false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // controller.submitForm();
  }
  @override
  Widget build(BuildContext context) {
    // Initialize controller

    return Scaffold(
      backgroundColor: Colors.white,
      appBar:  CustomAppBar(leading: CustomImageView(imagePath: AppIcons.arrowBack, color: AppColors.primary,), leadingBack: false, backgroundColor: Colors.white,),

      body: SafeArea(
        child: Stack(
          children: [
            GestureDetector(
              onTap: FocusScope.of(context).unfocus,
              child: LayoutBuilder(
               builder: (context, constraints){
                 return  SingleChildScrollView(
                   physics: const AlwaysScrollableScrollPhysics(),
                   child: Center(
                     child: Container(
                       color: Colors.white,
                       width: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                       child: Padding(
                         padding: const EdgeInsets.all(AppSizes.md),
                         child: Column(
                           mainAxisAlignment: MainAxisAlignment.start,
                           crossAxisAlignment: CrossAxisAlignment.start,
                           children: [
                             GestureDetector(onTap: (){
                               log(getData.read('userRecord'));
                             },child: Text("Please provide the following details", style: CustomTextStyles.h5.copyWith(color: AppColors.primary))),
                             Form(
                               key: controller.formKey, // Attach the form key to validate the form
                               child: Column(
                                 crossAxisAlignment: CrossAxisAlignment.start,
                                 children: [
                                   const SizedBox(height: AppSizes.spaceBtwItems,),

                                   LabelTextField(label: "First Name", hint: "Enter First name", controller: controller.firstName, validator: AppValidators.validateText, isMandatory: true,),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),

                                   LabelTextField(label: "Last Name", hint: "Enter last name", controller: controller.lastName, validator: AppValidators.validateText,isMandatory: true,),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),

                                   LabelTextField(label: "Email", hint: "Enter your Email", controller: controller.email, validator: AppValidators.validateEmail,isMandatory: true,),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),
                                   // Select role
                                   Text("Select role", style: CustomTextStyles.b4),
                                   const SizedBox(height: AppSizes.spaceSmall,),
                                   /// Radio button selection issue
                                   // CustomRadioButtonTile(
                                   //   width: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                   //   label: "Dentist",
                                   //   value: "Dentist",
                                   //   groupValue: controller.selectedOption.value,
                                   //    borderRadius: 12,
                                   //    onChanged: (value) {
                                   //      setState(() {
                                   //        controller.changeSelectedOption(value as String?);
                                   //      });
                                   //    },
                                   //   selectedColor: AppColors.primary,
                                   //   unselectedColor: AppColors.darkGrey,
                                   //
                                   // ),

                                   Obx(() => CustomRadioButtonTile(
                                     width: MediaQuery.of(context).size.width > 600 ? Get.size.width * 0.5 : Get.size.width,
                                     label: "Dentist",
                                     value: "Dentist",
                                     groupValue: controller.selectedDentist.value,
                                     borderRadius: 12,
                                     onChanged: (value) {
                                       controller.selectDentist(value as String?);
                                     },
                                     selectedColor: AppColors.primary,
                                     unselectedColor: AppColors.darkGrey,
                                   )),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),
                                   Obx(() => CustomDropdownWithRadio(
                                     dropdownWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                     buttonWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                     hintText: "Laboratory",
                                     items: const ["Owner", "Technician", "Delivery Boy"],
                                     selectedValue: controller.selectedLaboratory.value, // Reactive selectedOption
                                     onChanged: (value) {
                                       controller.selectLaboratory(value);
                                     },
                                   ),
                                   ),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),
                                   Obx(() => CustomDropdownWithRadio(
                                     buttonWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                     dropdownWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                     hintText: "Radiology Center",
                                     items: const ["Owner", "Technician"],
                                     selectedValue: controller.selectedRadiology.value, // Reactive selectedOption
                                     onChanged: (value) {
                                       controller.selectRadiology(value);
                                     },
                                   ),
                                   ),
                                   const SizedBox(height: AppSizes.spaceBtwItems,),

                                   // Obx(()=>   CustomDropdownWithRadio(
                                   //   buttonWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                   //   dropdownWidth: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                   //   hintText: "Material suppliers",
                                   //   items: const ["Owner", "Technician", "Delivery boy"],
                                   //   selectedValue: controller.selectedMaterialSupply.value, // Reactive selectedOption
                                   //   onChanged: (value) {
                                   //     controller.selectMaterialSupply(value);
                                   //   },
                                   // ),
                                   // ),
                                   const SizedBox(height: AppSizes.defaultSpace,),
                                   CustomElevatedButton(
                                       buttonStyle:  ButtonStyle(
                                           backgroundColor: MaterialStateProperty.all(AppColors.primary)),
                                       width: MediaQuery.of(context).size.width > 600 ? Get.size.width *0.5 : Get.size.width,
                                       text: "Submit",
                                       onPressed:() async {

                                         log("selectedDentist : ${controller.selectedDentist.value}" );
                                         log("selectedMaterialSupply : ${controller.selectedMaterialSupply.value}" );
                                         log("selectedRadiology : ${controller.selectedRadiology.value}" );
                                         log("selectedLaboratory : ${controller.selectedLaboratory.value}" );


                                         log(controller.firstName.text );
                                         log(controller.lastName.text);
                                         log(controller.email.text);

                                         //log(controller.signInController.userModel!.userRecord.id ?? "");

                                         String? selectedOption = (controller.selectedDentist.value?.isNotEmpty ?? false)
                                             ? controller.selectedDentist.value
                                             : (controller.selectedLaboratory.value?.isNotEmpty ?? false)
                                             ? controller.selectedLaboratory.value
                                             : (controller.selectedRadiology.value?.isNotEmpty ?? false)
                                             ? controller.selectedRadiology.value
                                             : (controller.selectedMaterialSupply.value?.isNotEmpty ?? false)
                                             ? controller.selectedMaterialSupply.value
                                             : 'No Role Selected';
                                         log("submit form method called");

                                         log("selectedOption : $selectedOption");
                                         if(selectedOption != null){
                                           showErrorMessage = await controller.submitForm();
                                           setState(() {
                                           });
                                         }
                                         else{
                                           Get.snackbar('Error', 'No Role Selected',
                                            backgroundColor: AppColors.primary3,
                                            colorText: Colors.white,
                                            duration: const Duration(seconds: 3),
                                            snackPosition: SnackPosition.TOP,
                                           );
                                         }
                                         // Call the submit method
                                       }
                                   ),
                                 ],
                               ),
                             ),
                           ],
                         ),
                       ),
                     ),
                   ),
                 );
               },
              ),
            ),
            if (showErrorMessage)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  color: Colors.red.shade100,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Expanded(
                            child: Text(
                              'For Non Dentist roles to login, please contact:',
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                showErrorMessage = false;
                              });
                            },
                            child: const Icon(Icons.close, color: Colors.red),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Icon(Icons.email, size: 18, color: Colors.red),
                          const SizedBox(width: 6),
                          const Expanded(
                            child: Text(
                              '<EMAIL>',
                              style: TextStyle(color: Colors.black87),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Clipboard.setData(const ClipboardData(text: '<EMAIL>'));
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Email copied')),
                              );
                            },
                            child: const Icon(Icons.copy, size: 18, color: Colors.black54),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          const Icon(Icons.phone, size: 18, color: Colors.red),
                          const SizedBox(width: 6),
                          const Expanded(
                            child: Text(
                              '9642711179',
                              style: TextStyle(color: Colors.black87),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Clipboard.setData(const ClipboardData(text: '9642711179'));
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Phone number copied')),
                              );
                            },
                            child: const Icon(Icons.copy, size: 18, color: Colors.black54),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}






