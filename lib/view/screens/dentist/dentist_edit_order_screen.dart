import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/utils/app_export.dart';
import '../../../controllers/dentist_controllers/dentist_create_order_controller.dart';
import '../../../controllers/dentist_controllers/dentist_order_controller.dart';
import '../../../data/models/dentist/dentist_home_data_model.dart';
import '../../../data/models/dentist/dentist_order_detail_model.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';
import 'dentist_editOrder_success_screen.dart';
import 'package:file_picker/file_picker.dart';

class DentistEditOrderScreen extends StatefulWidget {
  String? toOrgName;
  String? toOrgName1;
   DentistEditOrderScreen({super.key,this.toOrgName,this.toOrgName1});

  @override
  State<DentistEditOrderScreen> createState() =>
      _DentistEditOrderScreenState();
}

class _DentistEditOrderScreenState extends State<DentistEditOrderScreen> {
  late final String? serviceName;
  final DentistOrderController orderController = Get.find();
  DentistCreateOrderController orderController1 = Get.put(DentistCreateOrderController());

  //final DoctorServiceController doctorServiceController = Get.find();
  final _formKey = GlobalKey<FormState>();
  //final DentistCreateOrderController editorderController = Get.find();

  bool _orderDateError = false;
  bool _requiredDateError = false;
  bool showDateError = false;
  bool _shadeError = false;

  // Text Controllers
  late TextEditingController patientNameController;
  late TextEditingController patientIdController;
  late TextEditingController laboratoryController;
  late List<TextEditingController> serviceController;
  late TextEditingController orderDateController;
  late TextEditingController requiredDateController;
  late TextEditingController toothNameController;
  late List<TextEditingController> shadeController;
  late TextEditingController remarkController;
  late TextEditingController reasonforscanController;
  late TextEditingController ageController;
  String? selectedGender;
  String? fromTooth;
  String? toTooth;
  String? selectedShadeGuide;
  String? selectedVitaShade;
  String? selectedVita3DShade;
  String? selectedImpressionType;
  List<File> _files = [];

  late String patientname;
  late String laboratoryName;
  late String toothName;
  late String remark;
  late String reasonforscan;
  String? selectedValue;

  // List<DentistOrderInfoService>? service;
  List<String> items = ['White', 'Light yellow'];

  DateTime? orderDate;
  DateTime? requiredDate;

  String? selectedvalue;
  // String? selectedShade;
  // List<String> selectedServices = [];

  List<dynamic> selectedServices = [];
  List<dynamic> selectedServices1 = [];

  List<dynamic> selectedServicesConverted = [];


  double subTotal = 0.0;
  double tax = 0.0;
  double totalAmount = 0.0;

  void recalculateTotal() {
    subTotal = 0.0;
    final arguments = Get.arguments;
    int quantity = arguments['quantity'] ?? 1;

    for (var service in selectedServices1) {
      // Ensure price is always a valid number
      double price = double.tryParse(service!.price.toString()) ?? 0.0;
      subTotal += price * quantity;
    }

    tax = subTotal * 0.18; // Assuming 18% GST
    totalAmount = subTotal + tax;

    setState(() {}); // 🟣 Rebuild to reflect the changes
  }



  @override
  void initState() {
    super.initState();

    // Get the order ID passed from previous screen
    final arguments = Get.arguments;
    log("Received arguments: $arguments");
    patientname=arguments["PatientName"];
    laboratoryName = arguments["LaboratoryName"];

    toothName = arguments["ToothName"];
    if (arguments['toothMappings'] != null && arguments['toothMappings'].isNotEmpty) {
      fromTooth = arguments['toothMappings'][0].fromTooth;
      toTooth = arguments['toothMappings'][0].toTooth;
    }
    if (arguments['orderFiles'] != null && arguments['orderFiles'].isNotEmpty) {
      _files = arguments['orderFiles'].map<File>((file) => File(file.files.first)).toList();
    }
    log("Tooth Name: $toothName");
    if (toothName != null && toothName.contains('-')) {
      var parts = toothName.split('-');
      if (parts.length == 2) {
        fromTooth = parts[0];
        toTooth = parts[1];
      }
    }
    remark = arguments["remark"];
    reasonforscan = arguments["reasonforscan"];
    var age = arguments["age"];
    selectedGender = arguments["gender"];
    selectedShadeGuide = arguments["shade_type"];
    selectedImpressionType = arguments["impression_type"];
    selectedValue = arguments["Shade"] ?? "";
    if (selectedShadeGuide == 'Vita shade guide') {
      selectedVitaShade = arguments["shade"];
    } else if (selectedShadeGuide == 'Vita 3D master shade') {
      selectedVita3DShade = arguments["shade"];
    }


    var orderDateData = arguments["OrderDate"];
    if (orderDateData is String && orderDateData.isNotEmpty) {orderDate = DateFormat("dd-MM-yyyy").parse(orderDateData);
    } else if (orderDateData is DateTime) {
      orderDate = orderDateData;
    } else {
      orderDate = null;
    }

    // Handle Required Date
    var requiredDateData = arguments["RequiredDate"];
    if (requiredDateData is String && requiredDateData.isNotEmpty) {
      requiredDate = DateFormat("dd-MM-yyyy").parse(requiredDateData);
    } else if (requiredDateData is DateTime) {
      requiredDate = requiredDateData;
    } else {
      requiredDate = null;
    }

   // log("🔍 Get.arguments: ${Get.arguments}");


    final List<dynamic>? orderServicesRaw = Get.arguments?["Services"];

    if (orderServicesRaw != null) {
      // Step 1: Extract OrgService list
      selectedServices = orderServicesRaw.map((orderService) {
        if (orderService is OrderService) {
          return orderService.orgservice;
        } else if (orderService is Map<String, dynamic>) {
          return OrgService.fromJson(orderService);
        }
        return null;
      }).whereType<OrgService>().toList();

      log("✅ Selected Services from Order: ${selectedServices.map((s) => s.servicess?.serviceName ?? 'No Service Name').toList()}");
      log("✅ Selected Services from Order: ${selectedServices.map((s) => s.id).toList()}");

      // Step 2: Convert OrgService.servicess to Service objects
      selectedServices1 = selectedServices.map((orgService) {
        final serviceDetails = orgService.servicess;

        if (serviceDetails is ServiceDetails) {
          log("✅ Mapping OrgService ID: ${orgService.id}, ServiceName: ${serviceDetails.serviceName}");
          return Service(
            id: orgService.id , // Get the ID from OrgService
            servicename: serviceDetails.serviceName,
            price: orgService.price.toString(), // Map price if needed
          );
        }
        return null;
      }).whereType<Service>().toList();

      log("✅ Selected Services after Mapping: ${selectedServices1.map((s) => s?.servicename).toList()}");
      log("Selected Services IDs after Mapping: ${selectedServices1.map((s) => s?.id).toList()}");

    }


    patientNameController = TextEditingController(text: patientname);
    patientIdController = TextEditingController();
    laboratoryController = TextEditingController(text: laboratoryName);
    orderDateController = TextEditingController(text: orderDate != null ? DateFormat("dd-MM-yyyy").format(orderDate!) : "");
    requiredDateController = TextEditingController(text: requiredDate != null ? DateFormat("dd-MM-yyyy").format(requiredDate!) : "");
    toothNameController = TextEditingController(text: toothName);
    remarkController = TextEditingController(text:remark);
    reasonforscanController=TextEditingController(text: reasonforscan);
    ageController = TextEditingController(text: age.toString());
  }

  Future<void> _selectDate(
      BuildContext context,
      DateTime? currentValue,
      Function(DateTime) onDateSelected,
      ) async {
    final DateTime now = DateTime.now();

    final DateTime initialDate = currentValue ?? now;

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      onDateSelected(picked);
    }
  }

  @override
  Widget build(BuildContext context) {

    final Map<String, dynamic> orderData = Get.arguments ?? {};
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Edit Order',
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      body: GetBuilder<HomeController>(
        builder: (homeController) {

          return SafeArea(
            child: GestureDetector(
              onTap: FocusScope.of(context).unfocus,
              child: SingleChildScrollView(
                child: Form(
                    key: _formKey,
                    child: Center(
                      child: Container(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(AppSizes.md),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(
                                    height: AppSizes.sm,
                                  ),
                                  if(widget.toOrgName!="Material Supplier")

                                  LabelTextField(
                                    label: 'Patient Name',
                                    hint: 'Enter Patient Name',
                                    controller: patientNameController,
                                    validator: AppValidators.validateText,
                                  ),
                                  if(widget.toOrgName!="Material Supplier")
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  LabelTextField(
                                    label: getOrgLabel(widget.toOrgName1),
                                    hint: 'Enter Laboratory Name',
                                    controller: laboratoryController,
                                    validator: AppValidators.validateText,
                                    isDisabled: true,
                                  ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  const TextFieldLabel(label: 'Service Names'),

                                  MultiSelectField<dynamic>(
                                    label: "Select Services",
                                    textStyleLabel: CustomTextStyles.b4_1.copyWith(color: AppColors.darkGrey),
                                    itemMenuStyle: CustomTextStyles.b4_1,

                                    // Data for dropdown
                                    data: () {
                                      final List<dynamic> allOrganizations = [
                                        ...(homeController.dentistHomeDataModel?.dentalLaboratory ?? []),
                                        ...(homeController.dentistHomeDataModel?.radiology ?? []),
                                        ...(homeController.dentistHomeDataModel?.materialSupplier ?? []),
                                      ];

                                      final selectedOrganization = allOrganizations.firstWhere(
                                            (org) => (org as dynamic).name?.toLowerCase().trim() == laboratoryController.text.toLowerCase().trim(),
                                        orElse: () => null,
                                      );

                                      final List<dynamic> filteredServices = (selectedOrganization != null)
                                          ? ((selectedOrganization as dynamic).services as List<dynamic>?)
                                          ?.whereType<dynamic>()
                                          .toList() ?? []
                                          : [];

                                      // Debugging: log the ids to check if services are being matched correctly
                                      log("Default selected services IDs: ${selectedServices1.map((s) => s?.id).toList()}");
                                      log("Filtered services IDs: ${filteredServices.map((s) => s.id).toList()}");

                                      log("Filtered Services: ${filteredServices.map((s) => s.servicename).toList()}");

                                      // Mapping services to Choice<dynamic> for the dropdown data
                                      return filteredServices.map((service) {
                                        return Choice<dynamic>(
                                          service.id.toString(),
                                          service.servicename,
                                          metadata: service,
                                        );
                                      }).toList();
                                    },

                                    // On select, save the selected services
                                    onSelect: (List<Choice<dynamic>> selectedChoices, bool isFromDefaultData) {
                                      selectedServices1 = selectedChoices.map((choice) => choice.metadata).toList();

                                      // Log selected
                                      log("Updated selected services: ${selectedServices1.map((s) => s?.id).toList()}");

                                      // 🧮 Immediately recalculate total when selection changes
                                      recalculateTotal();
                                    },


                                    // Pass the selected services as default data (preselected services)
                                    defaultData: selectedServices1.map((service) {
                                      return Choice<dynamic>(
                                        service?.id.toString(),
                                        service!.servicename,
                                        metadata: service,
                                      );
                                    }).toList(),

                                    cleanCurrentSelection: false,
                                    singleSelection: false,
                                    useTextFilter: true,
                                    menuStyle: MenuStyle(
                                      shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      )),
                                      backgroundColor: const WidgetStatePropertyAll(Colors.white),
                                      maximumSize:  WidgetStatePropertyAll<Size>(
                                        Size(Get.size.width, 200), // Set width and height manually
                                      ),
                                    ),
                                    itemColor: ItemColor(
                                      selected: Colors.purple.withOpacity(0.2),
                                      unSelected: Colors.white,
                                    ),
                                    isDisabled: false,
                                  ),

                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  _buildDateField(orderDate, (date) {
                                    setState(() {
                                      orderDate = date;
                                      _orderDateError = false;
                                    });
                                  },
                                      'Order Date', _orderDateError,isDisabled: true),

                                  const SizedBox(height: AppSizes.defaultSpace),
                                  _buildDateField(requiredDate, (date) {
                                    requiredDate = date;
                                    _requiredDateError = false;

                                  }, 'Required Date', _requiredDateError),
                                  if (showDateError)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          top: AppSizes.sm, left: AppSizes.md),
                                      child: Text(
                                        "Please Select Date",
                                        style: CustomTextStyles.b6.copyWith(
                                          color: Colors.redAccent,
                                        ),
                                      ),
                                    ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  if(widget.toOrgName1=="Dental Laboratory")...[
                                  LabelTextField(
                                    label: 'Age',
                                    hint: 'Enter Age',
                                    controller: ageController,
                                    inputType: TextInputType.number,
                                    isMandatory: true,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Age is required';
                                      }
                                      if (int.tryParse(value) == null) {
                                        return 'Please enter a valid age';
                                      }
                                      if (int.parse(value) < 0 || int.parse(value) > 120) {
                                        return 'Age must be between 0 and 120';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Gender'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Male',
                                        groupValue: selectedGender,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedGender = value;
                                          });
                                        },
                                      ),
                                      const Text('Male'),
                                      Radio<String>(
                                        value: 'Female',
                                        groupValue: selectedGender,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedGender = value;
                                          });
                                        },
                                      ),
                                      const Text('Female'),
                                    ],
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Select Tooth'),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'From',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          selectedValue: fromTooth,
                                          onChanged: (value) {
                                            setState(() {
                                              fromTooth = value;
                                            });
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.spaceBtwItems),
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'To',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          selectedValue: toTooth,
                                          onChanged: (value) {
                                            setState(() {
                                              toTooth = value;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Shades'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Vita shade guide',
                                        groupValue: selectedShadeGuide,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeGuide = value;
                                          });
                                        },
                                      ),
                                      const Text('Vita shade guide'),
                                      Radio<String>(
                                        value: 'Vita 3D master shade',
                                        groupValue: selectedShadeGuide,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeGuide = value;
                                          });
                                        },
                                      ),
                                      const Text('Vita 3D master shade'),
                                    ],
                                  ),
                                  if (selectedShadeGuide == 'Vita shade guide')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        'A1 (Reddishbrown)', 'A2', 'A3', 'A4',
                                        'B1 (Reddish yellow)', 'B2', 'B3', 'B4',
                                        'C1 (Grey)', 'C2', 'C3', 'C4 (Reddish grey)',
                                        'D2', 'D3', 'D4'
                                      ],
                                      selectedValue: selectedVitaShade,
                                      onChanged: (value) {
                                        setState(() {
                                          selectedVitaShade = value;
                                        });
                                      },
                                    ),
                                  if (selectedShadeGuide == 'Vita 3D master shade')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        '1M-1', '1M-2', '2M-1', '2M-2', '2M-3',
                                        '2L-1.5', '2L-2.5', '2R- 1.5', '2R-2.5',
                                        '3L-1.5', '3L-2.5', '3M-1', '3M-2', '3M-3',
                                        '3R-1.5', '3R-2.5', '4L-1.5', '4L-2.5',
                                        '4M-1', '4M-2', '4M-3', '4R-1.5', '4R-2.5',
                                        '5M-1', '5M-2', '5M-3'
                                      ],
                                      selectedValue: selectedVita3DShade,
                                      onChanged: (value) {
                                        setState(() {
                                          selectedVita3DShade = value;
                                        });
                                      },
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Type of Impression'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Physical',
                                        groupValue: selectedImpressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedImpressionType = value;
                                          });
                                        },
                                      ),
                                      const Text('Physical'),
                                      Radio<String>(
                                        value: 'Digital',
                                        groupValue: selectedImpressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedImpressionType = value;
                                          });
                                        },
                                      ),
                                      const Text('Digital'),
                                    ],
                                  ),
                                  if (selectedImpressionType == 'Digital')
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        CustomTextFormField(
                                          hintText: _files.isEmpty ? 'No file chosen' : '${_files.length} file(s) selected',
                                          suffix: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                                            child: CustomElevatedButton(
                                              height: 34,
                                              width: 100,
                                              text: 'Choose',
                                              onPressed: _pickFiles,
                                              buttonStyle: ButtonStyle(shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)), padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))),
                                            ),
                                          ),
                                          validator: (value) {
                                            if (_files.isEmpty) {
                                              return 'Please upload a file';
                                            }
                                            for (var file in _files) {
                                              if (file.path.split('.').last.toLowerCase() != 'stl') {
                                                return 'Please upload a valid STL file';
                                              }
                                              if (file.lengthSync() > 10 * 1024 * 1024) {
                                                return 'File size should not exceed 10 MB';
                                              }
                                            }
                                            return null;
                                          },
                                        ),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        ..._files.map((file) => Row(
                                          children: [
                                            Expanded(child: Text(file.path.split('/').last)),
                                            IconButton(
                                              icon: const Icon(Icons.remove_circle),
                                              onPressed: () {
                                                setState(() {
                                                  _files.remove(file);
                                                });
                                              },
                                            ),
                                          ],
                                        )),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        const Text('(Upload up to 5 STL digital impression files)'),
                                      ],
                                    ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  )
                                  ],
                                  if(widget.toOrgName1=="Radiology")
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const TextFieldLabel(
                                        label: 'Reason for Scan',
                                        isMandatory: true,
                                      ),
                                      CustomTextFormField(
                                        maxLines: 3,
                                        hintText: 'Write Note...',
                                        controller: reasonforscanController,
                                      ),
                                      const SizedBox(height: AppSizes.defaultSpace),
                                    ],
                                  ),

                                  LabelTextField(
                                    isMandatory: false,
                                    label: 'Remarks',
                                    hint: 'Write Note...',
                                    maxLines: 4,
                                    controller: remarkController,
                                  ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  CustomElevatedButton(
                                    onPressed: () async {
                                      // patientname=arguments["PatientName"];
                                      // laboratoryName = arguments["LaboratoryName"];

                                      recalculateTotal();
                                      Map<String, dynamic> orderData1 = {
                                        "id": orderData['id'],
                                        'orderId': orderData['OrderId'],
                                        'patientName': patientNameController.text,
                                        'patientId': patientIdController.text,
                                        "toOrganization": orderData['toOrgId'],
                                        "serviceId": selectedServices1.map((service) => {
                                          "id": service?.id,
                                          "quantity": orderData['quantity'],
                                        }).toList(),
                                       'doctor_name': orderData['DoctorName'],
                                       'hospital_name': orderData['HospitalName'],
                                       'remarks': remarkController.text,
                                       'orderDate': orderDate != null ? DateFormat('yyyy-MM-dd').format(orderDate!) : '',
                                       'requiredDate': requiredDate != null ? DateFormat('yyyy-MM-dd').format(requiredDate!) : '',
                                       'laboratory_name': laboratoryController.text,
                                       'shade': selectedValue,
                                       'toothMappings': fromTooth != null && toTooth != null ? [{'from_tooth': fromTooth, 'to_tooth': toTooth, 'tooth_order': 1}] : [],
                                       'stl_files': _files,
                                       'impression_type': selectedImpressionType ?? '',
                                       'shade_type': selectedShadeGuide ?? '',
                                       'age': ageController.text,
                                       'gender': selectedGender ?? '',
                                       "reasonForScan": reasonforscanController.text,
                                       "userUUID": orderData['userId'],
                                       "sub_total": subTotal.toStringAsFixed(2),
                                       "tax": tax.toStringAsFixed(2),
                                       "total_amount": totalAmount.toStringAsFixed(2),
                                       "address": orderData['address']
                                  };

                                      log("Order Data to be sent: $orderData1");

                                      try {
                                        bool isSuccess = await orderController1.createOrder(orderData1);

                                        if (isSuccess) {
                                          // ✅ Update order status
                                          DentistOrderController dentistOrderController = Get.find();
                                          dentistOrderController.fetchStatus(status: 'processing');

                                          // ✅ Navigate to success screen only if successful
                                          Get.to(const DentistEditOrderSuccessScreen());
                                        }
                                      } catch (e) {
                                        Get.snackbar(
                                          "Error",
                                          "An unexpected error occurred: $e",
                                          snackPosition: SnackPosition.TOP,
                                          backgroundColor: AppColors.primary3,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                        );
                                      }
                                    },
                                    
                                    text: "Update",
                                  ),

                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    )),
              ),
            ),
          );
        }
      ),
    );
  }

  Widget _buildDateField(
      DateTime? date,
      Function(DateTime) onDateSelected,
      String label,
      bool showError, {
        bool isDisabled = false,
      })
  {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: isDisabled
              ? null
              : () => _selectDate(context, date, onDateSelected),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: isDisabled ? AppColors.lightGrey : Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null
                      ? DateFormat('dd-MM-yyyy').format(date)
                      : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(
                      color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2
                      .copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }

  String getOrgLabel(String? orgName) {
   // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supplier";
      default:
        return "Organization Name"; // Default fallback
    }
  }

  Future<void> _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      if (_files.length + result.files.length > 5) {
        Get.snackbar(
          'Limit Exceeded',
          'You can only upload a maximum of 5 files.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      for (var pickedFile in result.files) {
        if (_files.any((f) => f.path.split('/').last == pickedFile.name)) {
          Get.snackbar(
            'File Already Selected',
            '${pickedFile.name} has already been selected.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }
        final file = File(pickedFile.path!);
        final extension = file.path.split('.').last.toLowerCase();
        if (extension != 'stl') {
          Get.snackbar(
            'Invalid File Type',
            '${pickedFile.name} is not a valid STL file.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }

        if (file.lengthSync() > 10 * 1024 * 1024) {
          Get.snackbar(
            'File Too Large',
            '${pickedFile.name} exceeds the 10 MB size limit.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        } else {
          setState(() {
            _files.add(file);
          });
        }
      }
    } else {
      log('User canceled the picker');
    }
  }

}
