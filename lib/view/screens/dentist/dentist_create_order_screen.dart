import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:platix/data/models/dentist/doctor_service_info.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_checkout_screen.dart';
import 'package:platix/view/widgets/multi_select_dropdown/core/multi_select.dart';


import '../../../controllers/dentist_controllers/dentist_service_controller.dart';
import '../../../data/models/dentist/dentist_service_details_model.dart';

class DentistCreateOrderScreen extends StatefulWidget {
  final String? serviceName;
  const DentistCreateOrderScreen({super.key,this.serviceName});

  @override
  State<DentistCreateOrderScreen> createState() =>
      _DentistCreateOrderScreenState();
}

class _DentistCreateOrderScreenState extends State<DentistCreateOrderScreen> {
  final DoctorServiceController doctorServiceController = Get.find();
  final _formKey = GlobalKey<FormState>();

  bool _showServiceError = false;
  bool _orderDateError = false;
  bool _requiredDateError = false;
  bool _genderError = false;
  bool _toothError = false;
  bool _shadeGuideError = false;
  bool _shadeError = false;
  bool _impressionTypeError = false;
  bool _fileError = false;

  List<DoctorService>? services;
  List<Service>? services1;
  String? selectedShadeValue; // To hold selected shade value
  String? selectedToothValue; // To hold selected shade value

  // List<DoctorService> selectedServices = [];

  TextEditingController patientNameController = TextEditingController();
  TextEditingController patientIdController = TextEditingController();
  TextEditingController laboratoryNameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController remarksController = TextEditingController();
  TextEditingController reasonForScanController = TextEditingController();
  TextEditingController toothController = TextEditingController();
  TextEditingController serviceNameController = TextEditingController();
  TextEditingController ageController = TextEditingController();
  String? selectedGender;
  String? fromTooth;
  String? toTooth;
  String? selectedShadeGuide;
  String? selectedVitaShade;
  String? selectedVita3DShade;
  String? selectedImpressionType;
  List<File> _files = [];

  //TextEditingController quantityController = TextEditingController();

  double totalPrice = 0.0;
  DateTime? orderDate;
  DateTime? requiredDate;
  Service? selectedService;

  @override
  void initState() {
    if (Get.arguments != null && Get.arguments['services'] != null) {
      selectedService = Get.arguments['services'];
      if (selectedService != null) {
        serviceNameController.text = selectedService!.servicess!.servicename ?? '';
        doctorServiceController.selectedServices1.add(Choice(
          selectedService!.id,
          selectedService!.servicess!.servicename ?? '',
          metadata: selectedService,
        ));
      }
    } else {
      doctorServiceController.selectedServices1.value = [];
    }
    orderDate = DateTime.now(); // Initialize orderDate to today
    super.initState();
  }

  @override
  void dispose(){
    doctorServiceController.selectedServices1.value = [];
    super.dispose();
  }

  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected,
      String label, bool showError, {bool onlyToday = false, DateTime? minDate, DateTime? initialSelectedDate})
  {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: () => _selectDate(context, onDateSelected, onlyToday: onlyToday, minDate: minDate, initialSelectedDate: initialSelectedDate),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null
                      ? DateFormat('dd-MM-yyyy').format(date)
                      : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(
                      color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2
                      .copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: CustomTextStyles.b6.copyWith(color: Colors.red),
            ),
          ),
      ],
    );
  }


  Future<void> _selectDate(
      BuildContext context, Function(DateTime) onDateSelected,
      {bool onlyToday = false, DateTime? minDate, DateTime? initialSelectedDate}) async {
    final DateTime today = DateTime.now();
    final DateTime effectiveMinDate = minDate ?? today;
    final DateTime effectiveInitialDate = initialSelectedDate ?? effectiveMinDate;

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: effectiveInitialDate,
      firstDate: effectiveMinDate,
      lastDate: onlyToday ? today : DateTime(2100),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: AppColors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }


  @override
  Widget build(BuildContext context) {
    log("create order screen ${widget.serviceName}");
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Create Order',
        textColor: Colors.white,
        backgroundColor: AppColors.primary,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: FocusScope.of(context).unfocus,
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Center(
                child: Container(
                  width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(AppSizes.md),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(
                              height: AppSizes.sm,
                            ),
                            if(widget.serviceName !="Material Suppliers")
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                LabelTextField(
                                  label: 'Patient name',
                                  hint: 'Enter Patient Name',
                                  controller: patientNameController,
                                  isMandatory: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Patient name is required';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],
                            ),

                            if(widget.serviceName !="Material Suppliers")
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                LabelTextField(
                                  label: 'Patient ID',
                                  hint: 'Enter Patient ID',
                                  controller: patientIdController,
                                  isMandatory: true,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Patient ID is required';
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],
                            ),
                            if (widget.serviceName == "Dental Laboratory")
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  LabelTextField(
                                    label: 'Age',
                                    hint: 'Enter Age',
                                    controller: ageController,
                                    inputType: TextInputType.number,
                                    isMandatory: true,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Age is required';
                                      }
                                      if (int.tryParse(value) == null) {
                                        return 'Please enter a valid age';
                                      }
                                      if (int.parse(value) < 0 || int.parse(value) > 120) {
                                        return 'Age must be between 0 and 120';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Gender'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Male',
                                        groupValue: selectedGender,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedGender = value;
                                          });
                                        },
                                      ),
                                      const Text('Male'),
                                      Radio<String>(
                                        value: 'Female',
                                        groupValue: selectedGender,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedGender = value;
                                            _genderError = false;
                                          });
                                        },
                                      ),
                                      const Text('Female'),
                                    ],
                                  ),
                                  if (_genderError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Gender is required",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                ],
                              ),
                            LabelTextField(
                              label: getLabelText(widget.serviceName!),
                              hint: doctorServiceController.dentistServiceDetailsModel!.organization!.name,
                              controller: laboratoryNameController,
                              isMandatory: false,
                              isDisabled: true,
                            ),
                            const SizedBox(height: AppSizes.defaultSpace),
                            if (widget.serviceName == "Dental Laboratory")
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  LabelTextField(
                                    label: 'Service Order Name',
                                    hint: '',
                                    controller: serviceNameController,
                                    isDisabled: true,
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Select Tooth'),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'From',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          onChanged: (value) {
                                            setState(() {
                                              fromTooth = value;
                                              _toothError = false;
                                            });
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.spaceBtwItems),
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'To',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          onChanged: (value) {
                                            setState(() {
                                              toTooth = value;
                                              _toothError = false;
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (_toothError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select both 'From' and 'To' tooth",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Shades'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Vita shade guide',
                                        groupValue: selectedShadeGuide,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeGuide = value;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita shade guide'),
                                      Radio<String>(
                                        value: 'Vita 3D master shade',
                                        groupValue: selectedShadeGuide,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedShadeGuide = value;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita 3D master shade'),
                                    ],
                                  ),
                                  if (_shadeGuideError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select a shade guide",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  if (selectedShadeGuide == 'Vita shade guide')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        'A1 (Reddishbrown)', 'A2', 'A3', 'A4',
                                        'B1 (Reddish yellow)', 'B2', 'B3', 'B4',
                                        'C1 (Grey)', 'C2', 'C3', 'C4 (Reddish grey)',
                                        'D2', 'D3', 'D4'
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          selectedVitaShade = value;
                                          _shadeError = false;
                                        });
                                      },
                                    ),
                                  if (_shadeError && selectedShadeGuide == 'Vita shade guide')
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select a Vita shade",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  if (selectedShadeGuide == 'Vita 3D master shade')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        '1M-1', '1M-2', '2M-1', '2M-2', '2M-3',
                                        '2L-1.5', '2L-2.5', '2R- 1.5', '2R-2.5',
                                        '3L-1.5', '3L-2.5', '3M-1', '3M-2', '3M-3',
                                        '3R-1.5', '3R-2.5', '4L-1.5', '4L-2.5',
                                        '4M-1', '4M-2', '4M-3', '4R-1.5', '4R-2.5',
                                        '5M-1', '5M-2', '5M-3'
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          selectedVita3DShade = value;
                                          _shadeError = false;
                                        });
                                      },
                                    ),
                                  if (_shadeError && selectedShadeGuide == 'Vita 3D master shade')
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select a Vita 3D master shade",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Type of Impression'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Physical',
                                        groupValue: selectedImpressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedImpressionType = value;
                                            _impressionTypeError = false;
                                          });
                                        },
                                      ),
                                      const Text('Physical'),
                                      Radio<String>(
                                        value: 'Digital',
                                        groupValue: selectedImpressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            selectedImpressionType = value;
                                            _impressionTypeError = false;
                                          });
                                        },
                                      ),
                                      const Text('Digital'),
                                    ],
                                  ),
                                  if (_impressionTypeError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select the type of impression",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  if (selectedImpressionType == 'Digital')
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        CustomTextFormField(
                                          hintText: _files.isEmpty ? 'No file chosen' : '${_files.length} file(s) selected',
                                          suffix: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                                            child: CustomElevatedButton(
                                              height: 34,
                                              width: 100,
                                              text: 'Choose',
                                              onPressed: _pickFiles,
                                              buttonStyle: ButtonStyle(shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)), padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))),
                                            ),
                                          ),
                                          validator: (value) {
                                            if (_files.isEmpty) {
                                              return 'Please upload a file';
                                            }
                                            for (var file in _files) {
                                              if (file.path.split('.').last.toLowerCase() != 'stl') {
                                                return 'Please upload a valid STL file';
                                              }
                                              if (file.lengthSync() > 10 * 1024 * 1024) {
                                                return 'File size should not exceed 10 MB';
                                              }
                                            }
                                            return null;
                                          },
                                        ),
                                        if (_fileError)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 8.0),
                                            child: Text(
                                              "Please upload at least one STL file",
                                              style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                            ),
                                          ),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        ..._files.map((file) => Row(
                                          children: [
                                            Expanded(child: Text(file.path.split('/').last)),
                                            IconButton(
                                              icon: const Icon(Icons.remove_circle),
                                              onPressed: () {
                                                setState(() {
                                                  _files.remove(file);
                                                });
                                              },
                                            ),
                                          ],
                                        )),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        const Text('(Upload up to 5 STL digital impression files)'),
                                      ],
                                    ),
                                ],
                              )
                            else
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  TextFieldLabel(label: getLabel(widget.serviceName!)),
                                  Obx(() {
                                    if (doctorServiceController.dentistServiceDetailsModel == null) {
                                      return const Center(child: CircularProgressIndicator());
                                    }

                                return GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () => FocusScope.of(context).unfocus(),
                                  child: MultiSelectField(
                                    label: "Select Services",
                                    itemMenuStyle: CustomTextStyles.b4_1,
                                    data: () {
                                      return doctorServiceController.dentistServiceDetailsModel!.services!
                                          .map<Choice<Service>>(
                                            (service) => Choice<Service>(
                                          service.id,
                                          service.servicess?.servicename ?? "null",
                                          metadata: service,
                                        ),
                                      )
                                          .toList();
                                    },
                                    onSelect: (List<Choice<Service>> selectedChoices, bool isFromDefaultData) {
                                      doctorServiceController.selectedServices1.assignAll(selectedChoices);

                                      // Print selected service IDs properly
                                      List<String> selectedServiceIds = selectedChoices.map((choice) {
                                        return choice.metadata?.id ?? "Unknown Service";
                                      }).toList();

                                      log("Selected Service IDs: $selectedServiceIds");
                                    },
                                    defaultData: doctorServiceController.selectedServices1,
                                    cleanCurrentSelection: doctorServiceController.cleanCurrentSelection.value,
                                    singleSelection: false,
                                    useTextFilter: true,
                                    menuStyle: MenuStyle(
                                      shape: WidgetStatePropertyAll(
                                        RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                      ),
                                      backgroundColor: const WidgetStatePropertyAll(Colors.white),
                                      maximumSize: WidgetStatePropertyAll<Size>(
                                        Size(Get.size.width, 200), // Set width and height manually
                                      ),
                                    ),
                                    itemColor: ItemColor(
                                      selected: Colors.purple.withOpacity(0.2),
                                      unSelected: Colors.white,
                                    ),
                                    textStyleLabel: CustomTextStyles.b4_1.copyWith(color: AppColors.darkGrey),
                                    isDisabled: false,
                                  ),
                                );
                              }),
                              ],
                            ),

                            if (_showServiceError)
                              const Padding(
                                padding: EdgeInsets.only(top: 8.0),
                                child: Text(
                                  "Select a service",
                                  style: TextStyle(
                                      color: Colors.red, fontSize: 12),
                                ),
                              ),
                            const SizedBox(height: AppSizes.defaultSpace),
                            if(widget.serviceName =="Radiology Centers")
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const TextFieldLabel(
                                  label: 'Reason for Scan',
                                  isMandatory: true,
                                ),
                                CustomTextFormField(
                                  maxLines: 3,
                                  hintText: 'Write Note...',
                                  controller: reasonForScanController,
                                ),
                                const SizedBox(height: AppSizes.defaultSpace),
                              ],
                            ),

                            _buildDateField(orderDate, (date) {
                              setState(() {
                                orderDate = date;
                                _orderDateError = false;
                              });
                            }, 'Order Date', _orderDateError,
                               initialSelectedDate: orderDate,
                               minDate: DateTime.now()
                            ),

                            const SizedBox(height: AppSizes.defaultSpace),
                            _buildDateField(requiredDate, (date) {
                              requiredDate = date;
                              _requiredDateError = false;
                            }, 'Required Date', _requiredDateError,
                               initialSelectedDate: requiredDate,
                               minDate: orderDate ?? DateTime.now()
                            ),
                            const SizedBox(height: AppSizes.defaultSpace),
                            const TextFieldLabel(label: 'Remarks'),
                            CustomTextFormField(
                              maxLines: 3,
                              hintText: 'Write Note...',
                              controller: remarksController,
                              validator: (value) {
                                if (value != null && value.length < 5) {
                                  return "Remarks must be at least 5 characters.";
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: AppSizes.imageThumbSize),
                            CustomElevatedButton(
                              text: 'Continue',
                              onPressed: () {
                                setState(() {
                                  _orderDateError = orderDate == null;
                                  _requiredDateError = requiredDate == null;
                                  if (widget.serviceName == "Dental Laboratory") {
                                    _genderError = selectedGender == null;
                                    _toothError = fromTooth == null || toTooth == null;
                                    _shadeGuideError = selectedShadeGuide == null;
                                    _shadeError = (selectedShadeGuide == 'Vita shade guide' && selectedVitaShade == null) ||
                                        (selectedShadeGuide == 'Vita 3D master shade' && selectedVita3DShade == null);
                                    _impressionTypeError = selectedImpressionType == null;
                                    _fileError = selectedImpressionType == 'Digital' && _files.isEmpty;
                                  }
                                });

                                if (_formKey.currentState!.validate() &&
                                    doctorServiceController.selectedServices1.isNotEmpty &&
                                    !_orderDateError &&
                                    !_requiredDateError &&
                                    !_genderError &&
                                    !_toothError &&
                                    !_shadeGuideError &&
                                    !_shadeError &&
                                    !_impressionTypeError &&
                                    !_fileError) {

                                  List<Map<String, dynamic>> selectedServiceDetails =
                                  doctorServiceController.selectedServices1.map((selectedChoice) {
                                    final service = selectedChoice.metadata as Service;
                                    return {
                                      'id': service.id,
                                      'name': service.servicess?.servicename ?? "no name",
                                      'price': service.price,
                                      'quantity': 1,
                                    };
                                  }).toList();

                                  Map<String, dynamic> orderData = {
                                    'patientName': patientNameController.text,
                                    'patientId': patientIdController.text,
                                    'laboratoryName': laboratoryNameController.text,
                                    'selectedServices': selectedServiceDetails,
                                    'orderDate': orderDate != null
                                        ? DateFormat('yyyy-MM-dd').format(orderDate!)
                                        : '',
                                    'requiredDate': requiredDate != null
                                        ? DateFormat('yyyy-MM-dd').format(requiredDate!)
                                        : '',
                                    'shade': selectedShadeGuide == 'Vita shade guide' ? selectedVitaShade : selectedVita3DShade,
                                    'shades': selectedShadeGuide == 'Vita shade guide' ? selectedVitaShade : selectedVita3DShade,
                                    'toothMappings': fromTooth != null && toTooth != null ? [{'from_tooth': fromTooth, 'to_tooth': toTooth, 'tooth_order': 1}] : [],
                                    'age': ageController.text,
                                    'gender': selectedGender,
                                    'shade_type': selectedShadeGuide,
                                    'impression_type': selectedImpressionType,
                                    'remarks': remarksController.text,
                                    'reasonForScan': reasonForScanController.text,
                                    'selectedServiceDetails': selectedServiceDetails,
                                    'stl_files': _files,
                                  };
                                  if (widget.serviceName == "Dental Laboratory") {
                                    log('Order Data: $orderData');
                                    log("files uploaded: $_files");
                                  }
                                  Get.to(() => DentalPlaceOrderScreen(
                                    labName: doctorServiceController.dentistServiceDetailsModel!.organization!.name,
                                    serviceName: widget.serviceName,
                                    labId: doctorServiceController.dentistServiceDetailsModel!.organization!.id,
                                  ),
                                      arguments: orderData);
                                } else {
                                  setState(() {
                                    _showServiceError = doctorServiceController.selectedServices1.isEmpty;
                                  });
                                }
                              },

                            ),

                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String getLabelText(String serviceName) {
    switch (serviceName) {
      case 'Dental Laboratory':
        return 'Laboratory Name';
      case 'Radiology Centers':
        return 'Radiology Center Name';
      case 'Material Suppliers':
        return 'Material Supplier';
      default:
        return 'Laboratory Name';
    }
  }

  String getLabel(String serviceName) {
    if (serviceName == 'Material Suppliers') {
      return 'Material Name';
    } else if (serviceName == 'Dental Laboratory') {
      return 'Service Order Name';
    } else {
      return 'Service Name';
    }
  }

  Future<void> _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      if (_files.length + result.files.length > 5) {
        Get.snackbar(
          'Limit Exceeded',
          'You can only upload a maximum of 5 files.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      for (var pickedFile in result.files) {
        if (_files.any((f) => f.path.split('/').last == pickedFile.name)) {
          Get.snackbar(
            'File Already Selected',
            '${pickedFile.name} has already been selected.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }
        final file = File(pickedFile.path!);
        final extension = file.path.split('.').last.toLowerCase();
        if (extension != 'stl') {
          Get.snackbar(
            'Invalid File Type',
            '${pickedFile.name} is not a valid STL file.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }

        if (file.lengthSync() > 10 * 1024 * 1024) {
          Get.snackbar(
            'File Too Large',
            '${pickedFile.name} exceeds the 10 MB size limit.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        } else {
          setState(() {
            _files.add(file);
          });
        }
      }
    } else {
      log('User canceled the picker');
    }
  }

}
