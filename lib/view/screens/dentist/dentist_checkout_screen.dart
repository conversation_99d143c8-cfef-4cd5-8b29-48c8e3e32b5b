import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_editprofilescreen.dart';
import 'package:platix/view/screens/dentist/dentist_order_placed_success_screen.dart';
import 'package:platix/view/screens/dentist/dentist_payment_success_screen.dart';
import '../../../api/data_store.dart';
import '../../../controllers/dentist_controllers/dentist_create_order_controller.dart';
import '../../../controllers/dentist_controllers/dentist_order_controller.dart';
import '../../../controllers/dentist_controllers/dentist_profile_controller.dart';
import '../../../data/models/dentist/dentist_service_details_model.dart';
import '../../../data/models/user_model.dart';
import '../../widgets/custom_radio_button.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';

class DentalPlaceOrderScreen extends StatefulWidget {
  final String? serviceName;
  final String? labName;
  final String? labId;
   const DentalPlaceOrderScreen({super.key, this.labName,this.serviceName,this.labId});

  @override
  State<DentalPlaceOrderScreen> createState() => _DentalPlaceOrderScreenState();
}

class _DentalPlaceOrderScreenState extends State<DentalPlaceOrderScreen> {

  DentistCreateOrderController orderController = Get.put(DentistCreateOrderController());
   ProfileController profileController = Get.put(ProfileController());

  String latitude = "";
  String longitude = "";
  String fullAddress = ''; // Default full address

  late GoogleMapController mapController;
  LatLng showLocation = const LatLng(17.3850, 78.4867);

  String? selectedAddress;
  String? selectedPayment;

  final orderData = Get.arguments;
  late final selectedServiceDetails;

  final userRecord = getData.read("userRecord");
  final docName = getData.read("userRecord")["firstName"];
  final userId = getData.read("userRecord")["id"];
  String address = getData.read("userRecord")["address"]??"";
  late final String hospitalName;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _loadUserData();
    profileController.fetchSettings(); // This sets the platformFee

    selectedServiceDetails = orderData['selectedServices'] ?? [];
  }

  void _loadUserData() {
    var storedUser = getData.read("userRecord");

    if (storedUser != null) {
      if (storedUser is String) {
        storedUser = jsonDecode(storedUser);
      }

      if (storedUser is Map<String, dynamic>) {
        UserRecord user = UserRecord.fromJson(storedUser);

        hospitalName = user.organization?.name ?? "";


      }
    }
  }

  String formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return '';
    try {
      final date = DateTime.parse(dateStr);
      return DateFormat('dd-MM-yyyy').format(date);
    } catch (e) {
      return dateStr; // fallback in case of invalid format
    }
  }


  @override
  Widget build(BuildContext context) {
    // Ensure that selectedServiceDetails is not null and calculate total price
    double calculateSubtotal() {
      double subtotal = 0.0;

      for (var serviceDetail in selectedServiceDetails) {
        double price = 0.0;
        int quantity = 1;

        if (serviceDetail is Map<String, dynamic>) {
          // Correctly extract price and quantity from the passed map
          price = double.tryParse(serviceDetail['price']?.toString() ?? '0.0') ?? 0.0;
          quantity = int.tryParse(serviceDetail['quantity']?.toString() ?? '1') ?? 1;
        } else if (serviceDetail is Choice<Service>) {
          // Extract from metadata if it's a Choice<Service> (Failsafe handling)
          price = double.tryParse(serviceDetail.metadata!.price.toString()) ?? 0.0;
          quantity = 1; // Default to 1 (since Service model has no quantity)
        }

        subtotal += price * quantity;
      }

      return subtotal;
    }


    double calculateGST() {
      double subtotal = calculateSubtotal();
      return 0.18 * subtotal;  // 18% GST
    }

    // double calculateTotalAmount() {
    //   double subtotal = calculateSubtotal();
    //   double gst = calculateGST();
    //   double platformFee = 5.0; // Fixed platform fee
    //   return subtotal+gst+platformFee ;
    // }


    double calculateTotalAmount() {
      double subtotal = calculateSubtotal();

      if (selectedPayment == "Pay Now") {
        double gst = calculateGST();

       // double platformFee = profileController.platformFee.toDouble();

        return subtotal + gst;
      } else {

        return subtotal ;
      }
    }


    // Call the updated calculations
    double subtotal = calculateSubtotal();


    return Scaffold(
      appBar: const CustomAppBar(title: "Checkout", backgroundColor: AppColors.primary, textColor: AppColors.white),
      body: SingleChildScrollView(
        child: Center(
          child: Container(
            color: Colors.white,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                   Text(
                    'Order Details',
                    style: CustomTextStyles.b4.copyWith(fontWeight:  FontWeight.w700)
                  ),
                  const SizedBox(height: AppSizes.spaceSmall),
                  Container(
                    padding: const EdgeInsets.all(AppSizes.md),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withOpacity(0.16),
                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                    ),
                    child: Column(
                      children: [

                        _buildRow('Doctor Name', "Dr.$docName"),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Hospital Name', hospitalName),
                        if(widget.serviceName !="Material Suppliers")...[
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Patient Name', "${orderData['patientName']}"),
                        const SizedBox(height: AppSizes.spaceSmall)],
                        if(widget.serviceName !="Material Suppliers")
                        _buildRow('Patient ID', "${orderData['patientId']}"),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow(getServiceDisplayName(), widget.labName ?? orderData['laboratoryName']),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Order Date', formatDate(orderData['orderDate'])),
                        const SizedBox(height: AppSizes.spaceSmall),
                        _buildRow('Required Date', formatDate(orderData['requiredDate'])),

                        if (orderData['stl_files'] != null &&
                            (orderData['stl_files'] as List).isNotEmpty) ...[
                          const SizedBox(height: AppSizes.spaceSmall),
                          _buildStlFilesList(orderData['stl_files']),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  Text('Services:', style: CustomTextStyles.b4.copyWith(fontWeight:  FontWeight.w700)),
                  const SizedBox(height: 8.0),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                      boxShadow: [
                        BoxShadow(blurRadius: 4, color: AppColors.black.withOpacity(0.16)),
                      ],
                    ),
                    // child: ListView.builder(
                    //   shrinkWrap: true,
                    //   itemCount: selectedServiceDetails.length,
                    //   itemBuilder: (context, index) {
                    //     final service = selectedServiceDetails[index];
                    //
                    //     // Ensure quantity is always an int
                    //     int quantity = int.tryParse(service['quantity']?.toString() ?? '1') ?? 1;
                    //
                    //     // Ensure price is always a double
                    //     double price = double.tryParse(service['price']?.toString() ?? '0.0') ?? 0.0;
                    //
                    //     return SizedBox(
                    //       child: Column(
                    //         children: [
                    //           const SizedBox(height: AppSizes.sm2),
                    //           Padding(
                    //             padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                    //             child: Row(
                    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    //               children: [
                    //                 Column(
                    //                   crossAxisAlignment: CrossAxisAlignment.start,
                    //                   children: [
                    //                     Text(service['name'].toString(), style: CustomTextStyles.b6_3),
                    //                     const SizedBox(height: AppSizes.spaceExtraSmall),
                    //                     Text(
                    //                       "₹${(price * quantity).toStringAsFixed(2)}", // Ensuring correct price calculation
                    //                       style: CustomTextStyles.b6_3,
                    //                     ),
                    //                   ],
                    //                 ),
                    //                 Container(
                    //                   height: 32,
                    //                   width: 104,
                    //                   decoration: BoxDecoration(
                    //                     color: Colors.white,
                    //                     borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                    //                     border: Border.all(color: AppColors.primary),
                    //                   ),
                    //                   child: Row(
                    //                     children: [
                    //                       Padding(
                    //                         padding: const EdgeInsets.only(left: AppSizes.md, right: AppSizes.sm2),
                    //                         child: InkWell(
                    //                           child: CustomImageView(
                    //                             imagePath: AppIcons.remove,
                    //                           ),
                    //                           onTap: () {
                    //                             if (quantity > 1) {
                    //                               setState(() {
                    //                                 selectedServiceDetails[index]['quantity'] = (quantity - 1).toString();
                    //                               });
                    //                             }
                    //                           },
                    //                         ),
                    //                       ),
                    //                       Padding(
                    //                         padding: const EdgeInsets.symmetric(horizontal: 1.5),
                    //                         child: Text('$quantity', style: CustomTextStyles.b6_3),
                    //                       ),
                    //                       Padding(
                    //                         padding: const EdgeInsets.only(right: AppSizes.sm, left: AppSizes.sm2),
                    //                         child: InkWell(
                    //                           child: CustomImageView(
                    //                             imagePath: AppIcons.add,
                    //                             color: AppColors.primary,
                    //                           ),
                    //                           onTap: () {
                    //                             setState(() {
                    //                               selectedServiceDetails[index]['quantity'] = (quantity + 1).toString();
                    //                             });
                    //                           },
                    //                         ),
                    //                       ),
                    //                     ],
                    //                   ),
                    //                 ),
                    //               ],
                    //             ),
                    //           ),
                    //           const SizedBox(height: 11),
                    //           if (index != selectedServiceDetails.length - 1)
                    //             Container(height: 1, color: AppColors.grey),
                    //         ],
                    //       ),
                    //     );
                    //   },
                    // ),

                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: selectedServiceDetails.length,
                      itemBuilder: (context, index) {
                        final serviceDetail = selectedServiceDetails[index];

                        // Handle both Map<String, dynamic> and Choice<Service>
                        String serviceName = "";
                        double price = 0.0;
                        int quantity = 1;

                        if (serviceDetail is Map<String, dynamic>) {
                          // If it's a Map, extract values safely
                          serviceName = serviceDetail['name'].toString();
                          price = double.tryParse(serviceDetail['price']?.toString() ?? '0.0') ?? 0.0;
                          quantity = int.tryParse(serviceDetail['quantity']?.toString() ?? '1') ?? 1;
                        } else if (serviceDetail is Choice<Service>) {
                          // If it's a Choice<Service>, extract from metadata
                          serviceName = serviceDetail.metadata!.servicess!.servicename;
                          price = double.tryParse(serviceDetail.metadata!.price.toString()) ?? 0.0;
                          quantity = 1; // Default since there's no quantity in `Service`
                        }

                        return SizedBox(
                          child: Column(
                            children: [
                              const SizedBox(height: AppSizes.sm2),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(serviceName, style: CustomTextStyles.b6_3),
                                        const SizedBox(height: AppSizes.spaceExtraSmall),
                                        Text(
                                          "₹${(price * quantity).toStringAsFixed(2)}",
                                          style: CustomTextStyles.b6_3,
                                        ),
                                      ],
                                    ),
                                    Container(
                                      height: 32,
                                      width: 104,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                        border: Border.all(color: AppColors.primary),
                                      ),
                                      child: Row(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(left: AppSizes.md, right: AppSizes.sm2),
                                            child: InkWell(
                                              child: CustomImageView(imagePath: AppIcons.remove),
                                              onTap: () {
                                                if (quantity > 1) {
                                                  setState(() {
                                                    if (serviceDetail is Map<String, dynamic>) {
                                                      serviceDetail['quantity'] = (quantity - 1).toString();
                                                    }
                                                  });
                                                }
                                              },
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: 1.5),
                                            child: Text('$quantity', style: CustomTextStyles.b6_3),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(right: AppSizes.sm, left: AppSizes.sm2),
                                            child: InkWell(
                                              child: CustomImageView(imagePath: AppIcons.add, color: AppColors.primary),
                                              onTap: () {
                                                setState(() {
                                                  if (serviceDetail is Map<String, dynamic>) {
                                                    serviceDetail['quantity'] = (quantity + 1).toString();
                                                  }
                                                });
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 11),
                              if (index != selectedServiceDetails.length - 1)
                                Container(height: 1, color: AppColors.grey),
                            ],
                          ),
                        );
                      },
                    ),

                  ),
                  const SizedBox(height: AppSizes.spaceBtwItems),
                  Text("Shipping address", style: CustomTextStyles.b4.copyWith(fontWeight:  FontWeight.w700)),
                  const SizedBox(height: AppSizes.spaceSmall),

                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                      boxShadow: [
                        BoxShadow(blurRadius: 4, color: AppColors.black.withOpacity(0.16)),
                      ],
                    ),
                    child: ListTile(
                      title: Text(
                       address ,
                        style: CustomTextStyles.b6_3,
                      ),
                      trailing: widget.serviceName=="Dental Laboratory"?InkWell(
                        onTap: () async {
                          await Get.to(() => DentistEditProfilescreen(screenName: "checkout",));
                          setState(() {
                            address = getData.read("userRecord")["address"];
                          });
                        },
                        child:
                        Text("Change",style: CustomTextStyles.b6_3.copyWith(color: AppColors.primary),),
                      ):null
                    ),
                  ),

                  const SizedBox(height: AppSizes.spaceBtwItems),
                  Text("Bill Details", style: CustomTextStyles.b4.copyWith(fontWeight:  FontWeight.w700)),
                  const SizedBox(height: AppSizes.spaceSmall),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusLg),
                      boxShadow: [
                        BoxShadow(blurRadius: 4, color: AppColors.black.withOpacity(0.16)),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(AppSizes.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (selectedPayment == "Pay Now") ...[
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                            child: BillDetailRow(label: "Sub total", value: "₹${subtotal.toStringAsFixed(2)}"),
                          ),


                            const Divider(color: AppColors.grey, thickness: 0.5, height: 1,),


                            const Padding(
                              padding: EdgeInsets.symmetric(vertical: AppSizes.sm),
                              child: BillDetailRow(label: "GST", value: "18%"),
                            ),

                            const Divider(color: AppColors.grey, thickness: 0.5, height: 1,),


                            // Padding(
                            //   padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                            //   child: BillDetailRow(label: "Platform Fee", value: "₹${profileController.platformFee.toString()}"),
                            // ),

                            const Divider(color: AppColors.grey, thickness: 0.5, height: 1,),

                          ],

                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                            child: BillDetailRow(
                              label: "Total Amount",
                              value: "₹${calculateTotalAmount().toStringAsFixed(2)}",
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),


                  const SizedBox(height: AppSizes.spaceBtwItems),

                  // Text("Payment", style: CustomTextStyles.b4.copyWith(fontWeight:  FontWeight.w700)),
                  // const SizedBox(height: AppSizes.spaceSmall),
                  // CustomRadioButtonTile(
                  //   label: "Pay Now",
                  //   value: "Pay Now",
                  //   groupValue: selectedPayment,
                  //   borderRadius: 16,
                  //   onChanged: (value) {
                  //     setState(() {
                  //       selectedPayment = value as String;
                  //     });
                  //   },
                  //   selectedColor: AppColors.primary,
                  //   unselectedColor: AppColors.darkGrey,
                  //   shadowColor: AppColors.black.withOpacity(0.16),
                  // ),
                  //
                  // const SizedBox(height: AppSizes.spaceSmall),
                  //
                  // CustomRadioButtonTile(
                  //   label: "Pay at Radiology Center",
                  //   value: "Pay at Radiology Center",
                  //   groupValue: selectedPayment,
                  //   borderRadius: 16,
                  //   onChanged: (value) {
                  //     setState(() {
                  //       selectedPayment = value as String;
                  //     });
                  //   },
                  //   selectedColor: AppColors.primary,
                  //   unselectedColor: AppColors.darkGrey,
                  //   shadowColor: AppColors.black.withOpacity(0.16),
                  // ),
                  //
                  // const SizedBox(height: AppSizes.defaultSpace),
                  //
                  // CustomOutlinedButton(text: 'Pay after delivery',
                  //   onPressed: (){
                  //     Get.to(() => const DentistOrderPlacedSuccessScreen());
                  //   },
                  // ),

                  if (widget.serviceName != 'Dental Laboratory') ...[
                    Text("Payment", style: CustomTextStyles.b4.copyWith(fontWeight: FontWeight.w700)),
                    const SizedBox(height: AppSizes.spaceSmall),

                    CustomRadioButtonTile(
                      label: "Pay Now",
                      value: "Pay Now",
                      groupValue: selectedPayment,
                      borderRadius: 16,
                      onChanged: (value) {
                        setState(() {
                          selectedPayment = value as String;
                        });
                      },
                      selectedColor: AppColors.primary,
                      unselectedColor: AppColors.darkGrey,
                      shadowColor: AppColors.black.withOpacity(0.16),
                    ),

                    const SizedBox(height: AppSizes.spaceSmall),

                    CustomRadioButtonTile(
                      label: (widget.serviceName == "Material Suppliers")
                          ? "Pay after Delivery"
                          : "Pay at Radiology Center",
                      value: (widget.serviceName == "Material Suppliers")
                          ? "Pay after Delivery"
                          : "Pay at Radiology Center",
                      groupValue: selectedPayment,
                      borderRadius: 16,
                      onChanged: (value) {
                        setState(() {
                          selectedPayment = value as String;
                        });
                      },
                      selectedColor: AppColors.primary,
                      unselectedColor: AppColors.darkGrey,
                      shadowColor: AppColors.black.withOpacity(0.16),
                    ),
                  ],

                  const SizedBox(height: AppSizes.spaceBtwItems),

                  orderController.cashFreeLoading
                      ? const Center(child: CircularProgressIndicator())
                      : CustomElevatedButton(
                    onPressed: () async {
                      Map<String, dynamic> orderData1 = {
                        "patientName": orderData['patientName'],
                        "orderId": orderData['orderId'],
                        "patientId": orderData['patientId'],
                        "toOrganization": widget.labId ?? orderData['selected_toOrgId'],
                        "serviceId": selectedServiceDetails.map((service) => {
                          "id": service['id'],
                          "quantity": service['quantity']
                        }).toList(),
                        "orderDate": orderData['orderDate'],
                        "requiredDate": orderData['requiredDate'],
                        "toothMappings": orderData['toothMappings'],
                        "shade": orderData['shade'],
                        "remarks": orderData['remarks'],
                        "reasonForScan": orderData['reasonForScan'] ?? "",
                        "userUUID": userId,
                        "sub_total": subtotal,
                        "tax": 0.18 * subtotal,
                        "service_charges": profileController.platformFee,
                        "total_amount": calculateTotalAmount().toString(),
                        "address": address,
                        "age": orderData['age'],
                        "gender": orderData['gender'],
                        "shade_type": orderData['shade_type'],
                        "impression_type": orderData['impression_type'],
                        "stl_files": orderData['stl_files'],
                      };

                      print(orderData1);
                      log('Order Data checking: $orderData1');

                      try {
                        // Show loader before payment or order processing
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (_) => const Center(child: CircularProgressIndicator()),
                        );

                        if (selectedPayment == 'Pay Now' || widget.serviceName == "Material Supplier") {
                          setState(() {
                            orderController.cashFreeLoading = true;
                          });

                          log("total amount:::::::::${calculateTotalAmount()}");

                          final orderData = await orderController.createCashFreeOrder({
                            'amount': calculateTotalAmount().toString(),
                            'orderData': orderData1,
                          });

                          if (orderData != null && orderData.containsKey('orderId') && orderData.containsKey('sessionId')) {
                           String? orderId = orderData['orderId'];
                            //String? orderId = "ORDER_1746005356263";

                            String? paymentSessionId = orderData['sessionId'];
                            //String? paymentSessionId = "session_-2SNYOt_M5W3EqThqbpHLpdBSRNe59bVvlX6Q_SWSoCU6Dvh35LHoNhAR8sNSDjtMF8PKngn8zQKxsGgcTNxzX0QtQ4R6Sgi30X7ZNE-b829_CMJEzzUwTTrakmcwQpaymentpayment";

                            if (orderId != null && paymentSessionId != null) {
                              AppUtils.initiateCashFreePayment(orderId, paymentSessionId, (String orderId) async {
                                log("✅ Payment success callback triggered with orderId: $orderId");

                                // Additional logic after payment success, such as order creation
                                final additionalData = <String, dynamic>{'transactionId': orderId};
                                orderData1.addEntries(additionalData.entries);
                                log("Creating order...");
                                bool isSuccess = await orderController.createOrder(orderData1);
                                log("Order created with success: $isSuccess");

                                if (isSuccess) {
                                  log("Navigating to success screen...");
                                  DentistOrderController dentistOrderController = Get.find();
                                  dentistOrderController.fetchStatus(status: 'processing');

                                  // Delay before navigating to the success screen
                                  await Future.delayed(const Duration(milliseconds: 200));

                                  // Close the loader dialog
                                  Navigator.of(context).pop(); // Close the loader

                                  // Navigate to the success screen
                                  Get.offAll(const DentistPaymentSuccessScreen());
                                } else {
                                  Navigator.of(context).pop(); // Close the loader
                                }
                              }, (CFErrorResponse errorResponse, String orderId) {
                                log("Error: ${errorResponse.getMessage().toString()}");
                                Get.snackbar('Error', errorResponse.getMessage().toString(),
                                  backgroundColor: AppColors.primary3,
                                  colorText: Colors.white,
                                  duration: const Duration(seconds: 3),
                                  snackPosition: SnackPosition.TOP,
                                );

                                // Close the loader in case of error
                                Navigator.of(context).pop();
                              });
                            } else {
                              log("Error: orderId or paymentSessionId is null");
                              Navigator.of(context).pop(); // Close the loader
                            }
                          } else {
                            log("Error: orderData is null or missing keys");
                            Navigator.of(context).pop(); // Close the loader
                          }
                        } else {
                          bool isSuccess = await orderController.createOrder(orderData1);

                          if (isSuccess) {
                            DentistOrderController dentistOrderController = Get.find();
                            dentistOrderController.fetchStatus(status: 'processing');

                            // Delay before navigating to the success screen
                            await Future.delayed(const Duration(milliseconds: 200));

                            // Navigate to the success screen
                            Navigator.of(context).pop(); // Close the loader
                            Get.offAll(const DentistOrderPlacedSuccessScreen());
                          } else {
                            Navigator.of(context).pop(); // Close the loader
                          }
                        }
                      } catch (e) {
                        log("Exception: $e");
                        Get.snackbar(
                          "Error",
                          "An unexpected error occurred: $e",
                          snackPosition: SnackPosition.TOP,
                          backgroundColor: AppColors.primary3,
                          colorText: Colors.white,
                          duration: const Duration(seconds: 3),
                        );

                        // Close the loader in case of error
                        Navigator.of(context).pop();
                      }
                    },
                    isDisabled: widget.serviceName != "Dental Laboratory" && selectedPayment == null,
                    text: (widget.serviceName != 'Material Supplier') ? 'Confirm Order' : 'Pay Now',
                  ),



                  const SizedBox(height: AppSizes.defaultSpace),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 5, child: Text(label, style: CustomTextStyles.b4_1)),
        Text(': ', style: CustomTextStyles.b5),
        Expanded(flex: 6, child: Text(value, style: CustomTextStyles.b5)),
      ],
    );
  }

  Widget _buildStlFilesList(List<dynamic> files) {
    List<File> stlFiles = files.cast<File>();
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
            flex: 5,
            child: Text("STL Files", style: CustomTextStyles.b4_1)),
        Text(': ', style: CustomTextStyles.b5),
        Expanded(
          flex: 6,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: stlFiles.map((file) {
              return Text(
                file.path.split('/').last,
                style: CustomTextStyles.b5,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  String getServiceDisplayName() {
    if (widget.serviceName == 'Dental Laboratory') {
      return "Laboratory Name";
    } else if (widget.serviceName == 'Radiology Centers') {
      return "Radiology Name";
    } else if (widget.serviceName == 'Material Suppliers') {
      return "Material Supplier";
    }
    return 'N/A'; // Default if serviceName doesn't match
  }

}

class BillDetailRow extends StatelessWidget {
  final String label;
  final String value;


  const BillDetailRow({
    super.key,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: CustomTextStyles.b6_3.copyWith(color: AppColors.darkGrey),
          ),
          Text(
            value,
            style: CustomTextStyles.b6_1.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}

class ProcessingPaymentScreen extends StatelessWidget {
  const ProcessingPaymentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: Colors.black54,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 20),
            Text("Processing Payment...", style: TextStyle(color: Colors.white)),
          ],
        ),
      ),
    );
  }
}
