import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/dentist_controllers/dentist_order_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_edit_order_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../api/data_store.dart';
import '../../../controllers/dentist_controllers/dentist_create_order_controller.dart';
import '../../../controllers/dentist_controllers/dentist_home_controller.dart';
import '../../../data/models/user_model.dart';
import '../../widgets/full_screen_image_view.dart';

class DentistOrderDetailsScreen extends StatefulWidget {
  final String orderId;

  const DentistOrderDetailsScreen({super.key, required this.orderId});

  @override
  State<DentistOrderDetailsScreen> createState() =>
      _DentistOrderDetailsScreenState();
}

class _DentistOrderDetailsScreenState extends State<DentistOrderDetailsScreen> {
  final DentistOrderController dentistOrderController = Get.find();
  HomeController homeController = Get.put(HomeController());

  String latitude = "";
  String longitude = "";
  String fullAddress = ''; // Default full address

  late GoogleMapController mapController;
  LatLng showLocation = const LatLng(17.3850, 78.4867);
  String? selectedAddress;

  final doctorName = getData.read("userRecord")["firstName"];

  late final String hospitalName;
  String? laboratoryName;

  late String orderIdd;
  late String orderDate;
  late String paymentStatus;
  late String toothName;
  late String shade;

  void _loadUserData() {
    var storedUser = getData.read("userRecord");

    if (storedUser != null) {
      if (storedUser is String) {
        storedUser = jsonDecode(storedUser);
      }

      if (storedUser is Map<String, dynamic>) {
        UserRecord user = UserRecord.fromJson(storedUser);

        hospitalName = user.organization?.name ?? "";


      }
    }
  }


  double calculateGST() {
    double subtotal = double.tryParse(dentistOrderController
            .ordersDetailModel!.data!.subTotal
            .toString()) ??
        0.0;
    return 0.18 * subtotal; // 18% GST
  }

  double calculateTotalAmount() {
    double subtotal = double.tryParse(dentistOrderController
            .ordersDetailModel!.data!.subTotal
            .toString()) ??
        0.0;
    double gst = calculateGST();

    return double.parse((subtotal + gst).toStringAsFixed(2));
  }

  final dateFormat = DateFormat('dd-MM-yyyy'); // Adjust format as needed
  String formatDate(DateTime? date) {
    if (date == null) return 'N/A'; // Handle null values
    return dateFormat.format(date); // Format DateTime to String
  }

  DentistCreateOrderController orderController = Get.put(DentistCreateOrderController());

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _loadUserData();
    //orderIdd = widget.orderId;
    // laboratoryName= widget.orderId;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      dentistOrderController.getOrderDetails(widget.orderId);

      setState(() {
        orderIdd = widget.orderId;
        laboratoryName = dentistOrderController
                .ordersDetailModel?.data?.toOrganizationDetails?.name ??
            "Unknown";
        orderDate =
            dentistOrderController.ordersDetailModel?.data?.orderDate != null
                ? DateFormat('dd-MM-yyyy').format(
                    dentistOrderController.ordersDetailModel!.data!.orderDate)
                : "";

        toothName =
            dentistOrderController.ordersDetailModel?.data?.toothName ?? "";
        shade = dentistOrderController.ordersDetailModel?.data?.shades ?? "";
      });
    });

    // Extract the data from the arguments
    //  doctorName = arguments['doctorName'];
    //  hospitalName = arguments['hospitalName'];
    //  patientName = arguments['patientName'];
    //  patientId = arguments['patientId'];
    //  laboratoryName = arguments['laboratoryName'];
    //  services = arguments['services'];
    //  orderId = arguments['order_id'];
    //  orderDate = arguments['order_date'];
    //  paymentStatus = arguments['payment_status'];
    //  toothName = arguments["ToothName"];
    //  shade = arguments["Shade"];
    //  orderStatus = arguments["order_status"];
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
        appBar: const CustomAppBar(
          title: 'Order Details',
          backgroundColor: AppColors.primary,
          textColor: Colors.white,
        ),
        body: GetBuilder<DentistOrderController>(builder: (controller) {
          return dentistOrderController.isLoading == true
              ? const Center(
                  child: CircularProgressIndicator()
              ) // Show loader if data is null
              : dentistOrderController.ordersDetailModel == null
                  ? const Center(
                      child: Text('No Data'),
                    )
                  : SafeArea(
                      child: SingleChildScrollView(
                        child: Center(
                          child: Container(
                            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                            color: Colors.white,
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: AppSizes.md),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(
                                    height: AppSizes.spaceBtwItems,
                                  ),
                                  Row(
                                    children: [
                                      Text('Order Details',
                                          style: CustomTextStyles.b4.copyWith(height: 20 / 14)
                                      ),
                                      // const Spacer(),
                                      // if(dentistOrderController.ordersDetailModel!.data?.orderStatus =="processing")
                                      //   CustomElevatedButton(
                                      //     buttonTextStyle: TextStyle(fontSize: 16),
                                      //     text: 'Edit Order',
                                      //     height: 36,
                                      //     width: AppSizes.buttonWidth,
                                      //     buttonStyle: ButtonStyle(
                                      //         shape: WidgetStatePropertyAll(
                                      //           RoundedRectangleBorder(
                                      //               borderRadius: BorderRadius.circular(
                                      //                   AppSizes.borderRadiusSm)),
                                      //         ),
                                      //         padding: const WidgetStatePropertyAll(
                                      //             EdgeInsets.only(right: AppSizes.sm))),
                                      //
                                      //     // onTap: () {
                                      //     //   Get.to(() => DentistPaymentReportDetails(orderId: order.id));
                                      //     //   print("object");
                                      //     //   print(order.id);
                                      //     // },
                                      //
                                      //     // onPressed: (){
                                      //     //     Get.to(()=> DentistEditOrderScreen(orderId:orderIdd));
                                      //     // },
                                      //
                                      //     onPressed: () {
                                      //       Get.to(() =>  DentistEditOrderScreen(toOrgName: "Material Supplier",toOrgName1: dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.organizationType!.organizationType,),
                                      //
                                      //           arguments: {
                                      //             "order_id": widget.orderId,
                                      //             "id": dentistOrderController.ordersDetailModel!.data!.id,
                                      //             "DoctorName": doctorName,
                                      //             "patientId": dentistOrderController.ordersDetailModel!.data!.patientId,
                                      //             "reasonforscan": dentistOrderController.ordersDetailModel!.data!.reasonForScan,
                                      //             "toOrgId": dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.id??"null",
                                      //             "userId": dentistOrderController.ordersDetailModel!.data!.userDetails!.id,
                                      //             "HospitalName": hospitalName,
                                      //             "PatientName": dentistOrderController.ordersDetailModel!.data!.patientName,
                                      //             "LaboratoryName": dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.name,
                                      //             "OrderDate": orderDate,
                                      //             "Services": dentistOrderController.ordersDetailModel!.data!.orderServices, // ✅ Passing services list
                                      //             "ToothName": dentistOrderController.ordersDetailModel!.data!.toothName??"null",
                                      //             "Shade": dentistOrderController.ordersDetailModel!.data!.shades,
                                      //             "RequiredDate":dentistOrderController.ordersDetailModel!.data!.requiredDate,
                                      //             "subTotal":dentistOrderController.ordersDetailModel!.data!.subTotal,
                                      //             "tax":dentistOrderController.ordersDetailModel!.data!.serviceCharges,
                                      //             "totalAmount":dentistOrderController.ordersDetailModel!.data!.totalAmount,
                                      //             "address":dentistOrderController.ordersDetailModel!.data!.userDetails!.address,
                                      //             "quantity":dentistOrderController.ordersDetailModel!.data!.orderServices!.first.quantity,
                                      //             "remark":dentistOrderController.ordersDetailModel!.data!.remarks,
                                      //           });
                                      //
                                      //     },
                                      //   )
                                    ],
                                  ),
                                  const SizedBox(height: AppSizes.spaceSmall,),
                                  Container(
                                    padding: const EdgeInsets.all(AppSizes.md),
                                    decoration: BoxDecoration(
                                      color: AppColors.primary.withOpacity(0.16),
                                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                    ),
                                    child: Column(
                                      children: [
                                        _buildRow(
                                            'Order Id', dentistOrderController.ordersDetailModel?.data?.orderId ?? ''),
                                        const SizedBox(height: AppSizes.spaceSmall,),
                                        _buildRow('Doctor Name', "Dr.$doctorName"),
                                        const SizedBox(height: AppSizes.spaceSmall,),
                                        _buildRow('Hospital Name', hospitalName),
                                        const SizedBox(height: AppSizes.spaceSmall,),
                                        if (dentistOrderController.ordersDetailModel?.data?.toOrganizationDetails?.organizationType?.organizationType != "Material Supplier" &&
                                            (dentistOrderController.ordersDetailModel?.data?.patientName.isNotEmpty ?? false ||
                                                dentistOrderController.ordersDetailModel!.data!.patientId.isNotEmpty)) ...[
                                          const SizedBox(height: AppSizes.spaceSmall),
                                          if (dentistOrderController.ordersDetailModel?.data?.patientName.isNotEmpty ?? false)
                                            _buildRow('Patient Name', dentistOrderController.ordersDetailModel?.data?.patientName ?? ''),

                                          if (dentistOrderController.ordersDetailModel?.data?.patientId.isNotEmpty ?? false) ...[
                                            const SizedBox(height: AppSizes.spaceSmall),
                                            _buildRow('Patient Id', dentistOrderController.ordersDetailModel?.data?.patientId ?? ''),
                                          ],
                                          const SizedBox(height: AppSizes.spaceSmall),

                                        ],


                                        _buildRow(getOrgLabel(dentistOrderController.ordersDetailModel?.data?.toOrganizationDetails?.organizationType?.organizationType),
                                            dentistOrderController.ordersDetailModel?.data?.toOrganizationDetails?.name ?? "null"),
                                        const SizedBox(height: AppSizes.spaceSmall,),
                                        _buildRow('Order Date',
                                          formatDate(dentistOrderController.ordersDetailModel?.data?.orderDate),
                                        ),
                                        const SizedBox(height: AppSizes.spaceSmall,),

                                        _buildRow('Required Date',
                                          formatDate(dentistOrderController.ordersDetailModel?.data?.requiredDate),
                                        ),

                                        const SizedBox(height: AppSizes.spaceSmall,),

                                        // if(dentistOrderController.ordersDetailModel!.data!.paymentStatus=="inProgress")
                                        _buildRow(
                                            'Payment Status',

                                           "${dentistOrderController.ordersDetailModel?.data?.paymentStatus[0].toUpperCase()}${dentistOrderController.ordersDetailModel?.data?.paymentStatus.substring(1)}"
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    height: AppSizes.spaceBtwItems,
                                  ),
                                  if (dentistOrderController.ordersDetailModel?.data?.orderFiles.isNotEmpty ?? false)
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Uploaded Files',
                                          style: CustomTextStyles.b4.copyWith(fontWeight: FontWeight.w700),
                                        ),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        buildUploadedFiles(),
                                      ],
                                    ),
                                  const SizedBox(
                                    height: AppSizes.spaceBtwItems,
                                  ),
                                  Text('Services',
                                      style: CustomTextStyles.b4.copyWith(height: 20 / 14)),
                                  const SizedBox(height: AppSizes.spaceSmall,),
                                  Container(
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(
                                            AppSizes.borderRadiusSm),
                                        boxShadow: [
                                          BoxShadow(
                                            blurRadius: 4,
                                            color: AppColors.black.withOpacity(0.16),
                                          )
                                        ]
                                    ),
                                    child: Column(
                                      children: List.generate(
                                          dentistOrderController.ordersDetailModel?.data?.orderServices.length ??
                                              0, (index) {
                                        final service = dentistOrderController.ordersDetailModel?.data?.orderServices[index];

                                        // log("🛠 Service Item: ${jsonEncode(service)}"); // Log each service

                                        return SizedBox(
                                          height: 56,
                                          child: Column(
                                            children: [
                                              const SizedBox(height: 10),
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                                                child: Row(
                                                  children: [
                                                    Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                            service?.orgservice?.servicess?.serviceName ?? "No Name",
                                                            style: CustomTextStyles.b6_3),
                                                        const SizedBox(height: AppSizes.spaceExtraSmall),
                                                        Text(
                                                            "₹${service?.orgservice?.price ?? 0}",
                                                            style: CustomTextStyles.b6_3),
                                                      ],
                                                    ),
                                                    const Spacer(),
                                                    Text('Qty',
                                                        style: CustomTextStyles.b6_3
                                                    ),
                                                    Text(': ',
                                                        style: CustomTextStyles.b6_3
                                                    ),
                                                    Text(
                                                        '${service?.quantity ?? 0}',
                                                        style: CustomTextStyles.b6_3
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              const SizedBox(height: 11),
                                              if (index != dentistOrderController.ordersDetailModel!.data!.orderServices.length - 1)
                                                Container(
                                                  height: 1,
                                                  color: AppColors.grey,
                                                ),
                                            ],
                                          ),
                                        );
                                      }),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: AppSizes.spaceBtwItems,
                                  ),
                                  Text("Bill Details",
                                      style: CustomTextStyles.b4.copyWith(height: 20 / 14)),
                                  const SizedBox(
                                    height: AppSizes.spaceSmall,
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                      boxShadow: [
                                        BoxShadow(
                                          blurRadius: 4,
                                          color: AppColors.black.withOpacity(0.16),
                                        ),
                                      ],
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(AppSizes.md),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          ...(dentistOrderController.ordersDetailModel!.data!.orderStatus == "completed" ||
                                                  (dentistOrderController.ordersDetailModel!.data!.orderStatus == "processing" &&
                                                      dentistOrderController.ordersDetailModel!.data!.paymentStatus == "paid"
                                                  )
                                              ? [
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                                                    child: BillDetailRow(
                                                      label: "Sub total",
                                                      value: dentistOrderController.ordersDetailModel!.data!.subTotal.toString(),
                                                    ),
                                                  ),
                                                  CustomPaint(
                                                    painter: DashedBorderPainter(color: AppColors.grey,
                                                            strokeWidth: 0.8,
                                                            gap: 5,
                                                            borderRadius: BorderRadius.zero
                                                    ),
                                                    child: const SizedBox(
                                                        height: 0,
                                                        width: double.infinity
                                                    ),
                                                  ),
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: AppSizes.sm
                                                    ),
                                                    child: BillDetailRow(label: "GST", value: "${dentistOrderController.ordersDetailModel!.data!.tax.toString()}"
                                                    ),
                                                  ),
                                                  CustomPaint(
                                                    painter: DashedBorderPainter(
                                                            color: AppColors.grey,
                                                            strokeWidth: 0.8,
                                                            gap: 5,
                                                            borderRadius: BorderRadius.zero
                                                    ),
                                                    child: const SizedBox(
                                                        height: 0,
                                                        width: double.infinity),
                                                  ),
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                                    child: BillDetailRow(
                                                      label: "Total Amount",
                                                      value: calculateTotalAmount().toString(),
                                                      // value: dentistOrderController.ordersDetailModel!.data!.paymentStatus == "paid"
                                                      //     ? dentistOrderController.ordersDetailModel!.data!.totalAmount.toString()
                                                      //     : calculateTotalAmount().toString(),
                                                    ),
                                                  ),
                                                ]
                                              : [
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                                                    child: BillDetailRow(
                                                      label: "Total Amount",
                                                      value: dentistOrderController.ordersDetailModel!.data!.subTotal.toString(),
                                                    ),
                                                  ),
                                                ]),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: AppSizes.spaceBtwItems,),
                                  Text("Shipping address",
                                      style: CustomTextStyles.b4.copyWith(height: 20 / 14)),
                                  const SizedBox(
                                    height: AppSizes.spaceSmall,
                                  ),
                                  Container(
                                    padding: const EdgeInsets.all(0),
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(
                                            AppSizes.borderRadiusSm),
                                        boxShadow: [
                                          BoxShadow(
                                            blurRadius: 4,
                                            color: AppColors.black
                                                .withOpacity(0.16),
                                          )
                                        ]),
                                    child: ListTile(
                                      title: Text(
                                        selectedAddress ??
                                            dentistOrderController
                                                .ordersDetailModel!
                                                .data
                                                ?.address ??
                                            "no address",
                                        // Use the address if available
                                        style: CustomTextStyles.b6_3,
                                      ),

                                      // trailing: Text("Change", style: CustomTextStyles.b6_3.copyWith(color: AppColors.primary),),
                                    ),
                                  ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),

                                  if(dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails!.organizationType!.organizationType=="Radiology" &&
                                      dentistOrderController.ordersDetailModel!.data!.orderStatus == "completed")

                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                          'Uploaded Images',
                                          style: CustomTextStyles.b4.copyWith(fontWeight: FontWeight.w700)
                                      ),
                                      const SizedBox(
                                        height: AppSizes.spaceSmall,
                                      ),
                                      buildUploadedImages()
                                    ],
                                  ),
                                  const SizedBox(
                                    height: AppSizes.spaceSmall,
                                  ),
                                  Center(
                                    child: dentistOrderController.ordersDetailModel!.data!.orderStatus == "cancelled"
                                            ? Row(
                                                children: [
                                                  const Text(
                                                    "Order Status:",
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        color: AppColors.grey1,
                                                        fontWeight: FontWeight.w400),
                                                  ),
                                                  const SizedBox(
                                                      width: AppSizes.spaceExtraSmall),
                                                  Text(
                                                    "${dentistOrderController.ordersDetailModel!.data!.orderStatus[0].toUpperCase()}${dentistOrderController.ordersDetailModel!.data!.orderStatus.substring(1)}",
                                                    style: const TextStyle(
                                                        fontSize: 14,
                                                        color: AppColors.red,
                                                        fontWeight: FontWeight.w400),
                                                  ),
                                                ],
                                              )
                                            : dentistOrderController.ordersDetailModel!.data!.paymentStatus == "processing" &&
                                                    dentistOrderController.ordersDetailModel!.data!.orderStatus == "completed"
                                                ? orderController.cashFreeLoading
                                                    ? const Center(
                                                        child: CircularProgressIndicator())
                                                    : CustomElevatedButton(
                                                        width: MediaQuery.of(context).size.width > 600
                                                            ? Get.size.width * 0.8
                                                            : Get.size.width,
                                                        onPressed:
                                                            () async {
                                                          try {
                                                            setState(() {
                                                              orderController.cashFreeLoading = true;
                                                            });

                                                            final orderData = await orderController.createCashFreeOrder({
                                                              'amount': calculateTotalAmount().toString(),
                                                              'orderData': {
                                                                'toOrganization': dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.id ?? null,
                                                              }
                                                            }
                                                            );

                                                            if (orderData != null &&
                                                                orderData.containsKey('orderId') &&
                                                                orderData.containsKey('sessionId')) {
                                                              String? orderId = orderData['orderId'];
                                                              //String? orderId = "ORDER_1746005356263";
                                                              String? paymentSessionId = orderData['sessionId'];
                                                              //String? paymentSessionId = "session_-2SNYOt_M5W3EqThqbpHLpdBSRNe59bVvlX6Q_SWSoCU6Dvh35LHoNhAR8sNSDjtMF8PKngn8zQKxsGgcTNxzX0QtQ4R6Sgi30X7ZNE-b829_CMJEzzUwTTrakmcwQpaymentpayment";
                                                              String amount = calculateTotalAmount().toString();

                                                              if (orderId != null &&
                                                                  paymentSessionId != null) {
                                                                AppUtils.initiateCashFreePayment(
                                                                    orderId,
                                                                    paymentSessionId,
                                                                    (String orderId) async {
                                                                  log("hhhhhhhhhhhhh");
                                                                  log(orderId);
                                                                  // ✅ Call Payment API after successful payment
                                                                  await dentistOrderController.makePayment(
                                                                      orderId: dentistOrderController.ordersDetailModel!.data!.id,
                                                                      transactionId: orderId,
                                                                      amount: amount);
                                                                  setState(
                                                                      () {
                                                                    orderController.cashFreeLoading = false;
                                                                  });
                                                                }, (CFErrorResponse errorResponse,
                                                                        String orderId) {
                                                                  Get.snackbar(
                                                                      'Error',
                                                                      errorResponse.getMessage().toString(),
                                                                      backgroundColor: AppColors.primary3,
                                                                      colorText: Colors.white,
                                                                      duration: const Duration(seconds: 3),
                                                                      snackPosition: SnackPosition.TOP,
                                                                   );

                                                                  setState(
                                                                      () {
                                                                    orderController.cashFreeLoading =
                                                                        false;
                                                                  });
                                                                });
                                                              } else {
                                                                log("Error: orderId or paymentSessionId is null");
                                                                setState(() {
                                                                  orderController.cashFreeLoading = false;
                                                                });
                                                              }
                                                            } else {
                                                              log("Error: orderData is null or missing keys");
                                                              setState(() {
                                                                orderController.cashFreeLoading = false;
                                                              });
                                                            }
                                                          } catch (e) {
                                                            log("Exception: $e");
                                                            Get.snackbar(
                                                              "Error",
                                                              "An unexpected error occurred: $e",
                                                              backgroundColor: AppColors.primary3,
                                                              colorText: Colors.white,
                                                              duration: const Duration(seconds: 3),
                                                              snackPosition: SnackPosition.TOP,
                                                            );
                                                            setState(() {
                                                              orderController.cashFreeLoading = false;
                                                            });
                                                          }
                                                        },
                                                        text: 'Pay now',

                                                      )
                                                : dentistOrderController.ordersDetailModel!.data?.orderStatus == "processing"
                                                    ?
                                        Row(
                                          children: [
                                            // If NOT paid: show both Cancel & Edit
                                            if (dentistOrderController.ordersDetailModel?.data?.paymentStatus != "paid") ...[
                                              CustomElevatedButton(
                                                buttonStyle: const ButtonStyle(
                                                  backgroundColor: WidgetStatePropertyAll(AppColors.primary2),
                                                ),
                                                width: MediaQuery.of(context).size.width > 600
                                                    ? Get.size.width * 0.36
                                                    : Get.size.width * 0.45,
                                                onPressed: () {
                                                  MediaQuery.of(context).size.width > 600 ? cancelSheetWeb(context) : cancelOrderSheet();
                                                },
                                                text: 'Cancel Order',
                                                buttonTextStyle: const TextStyle(color: AppColors.primary),
                                              ),
                                              const Spacer(),
                                              orderController.cashFreeLoading
                                                  ? const Center(child: CircularProgressIndicator())
                                                  : CustomElevatedButton(
                                                width: MediaQuery.of(context).size.width > 600
                                                    ? Get.size.width * 0.36
                                                    : Get.size.width * 0.45,
                                                onPressed: () {
                                                  Get.to(
                                                        () => DentistEditOrderScreen(
                                                      toOrgName: "Material Supplier",
                                                      toOrgName1: dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.organizationType!.organizationType,
                                                    ),
                                                    arguments: {
                                                      "order_id": widget.orderId,
                                                      "id": dentistOrderController.ordersDetailModel!.data!.id,
                                                      "DoctorName": doctorName,
                                                      "patientId": dentistOrderController.ordersDetailModel!.data!.patientId,
                                                      "reasonforscan": dentistOrderController.ordersDetailModel!.data!.reasonForScan,
                                                      "toOrgId": dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.id ?? "null",
                                                      "userId": dentistOrderController.ordersDetailModel!.data!.userDetails!.id,
                                                      "HospitalName": hospitalName,
                                                      "PatientName": dentistOrderController.ordersDetailModel!.data!.patientName,
                                                      "LaboratoryName": dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.name,
                                                      "OrderDate": dentistOrderController.ordersDetailModel!.data!.orderDate,
                                                      "Services": dentistOrderController.ordersDetailModel!.data!.orderServices,
                                                      "ToothName": dentistOrderController.ordersDetailModel!.data!.toothName ?? "null",
                                                      "Shade": dentistOrderController.ordersDetailModel!.data!.shades,
                                                      "RequiredDate": dentistOrderController.ordersDetailModel!.data!.requiredDate,
                                                      "subTotal": dentistOrderController.ordersDetailModel!.data!.subTotal,
                                                      "tax": dentistOrderController.ordersDetailModel!.data!.serviceCharges,
                                                      "totalAmount": dentistOrderController.ordersDetailModel!.data!.totalAmount,
                                                      "address": dentistOrderController.ordersDetailModel!.data!.userDetails!.address,
                                                      "quantity": dentistOrderController.ordersDetailModel!.data!.orderServices.first.quantity,
                                                      "remark": dentistOrderController.ordersDetailModel!.data!.remarks,
                                                      "age": dentistOrderController.ordersDetailModel!.data!.age,
                                                      "gender": dentistOrderController.ordersDetailModel!.data!.gender,
                                                      "shade_type": dentistOrderController.ordersDetailModel!.data!.shade_type,
                                                      "impression_type": dentistOrderController.ordersDetailModel!.data!.impression_type,
                                                      "toothMappings": dentistOrderController.ordersDetailModel!.data!.toothMappings,
                                                      "orderFiles": dentistOrderController.ordersDetailModel!.data!.orderFiles,
                                                    },
                                                  );
                                                },
                                                text: 'Edit Order',
                                              ),
                                            ]
                                            // If paid: show Cancel full width
                                            else ...[

                                              CustomElevatedButton(
                                                buttonStyle: const ButtonStyle(
                                                  backgroundColor: WidgetStatePropertyAll(AppColors.primary2),
                                                ),
                                                width: Get.size.width-32,
                                                onPressed: () {
                                                  MediaQuery.of(context).size.width > 600 ? cancelSheetWeb(context) : cancelOrderSheet();
                                                },
                                                text: 'Cancel Order',
                                                buttonTextStyle: const TextStyle(color: AppColors.primary),
                                              ),
                                            ],
                                          ],
                                        )
                                            : (dentistOrderController.ordersDetailModel!.data?.paymentStatus.toLowerCase() != "paid" &&dentistOrderController.ordersDetailModel!.data!.toOrganizationDetails?.organizationType!.organizationType!="Radiology"
                                            ? CustomElevatedButton(
                                          onPressed: () {
                                            MediaQuery.of(context).size.width > 600 ? cancelSheetWeb(context) : cancelOrderSheet();
                                          },
                                          text: 'Cancel Order',
                                        )
                                            : const SizedBox()
                                        )
                                  ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
        }
        )
    );
  }

  Widget _buildRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 4,
          child: Text(
            label,
            style: CustomTextStyles.b4_1,
          ),
        ),
        Text(
          ': ',
          style: CustomTextStyles.b5,
        ),
        Expanded(
          flex: 6,
          child: Text(
            value,
            style: CustomTextStyles.b5,
          ),
        ),
      ],
    );
  }
  Future cancelOrderSheet() {
    return Get.bottomSheet(
      Container(
        height: 262,
        width: double.infinity,

        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Cancel Order",
              style: CustomTextStyles.h6,
            ),
            const SizedBox(
              height: AppSizes.spaceBtwList,
            ),
            Container(
              height: 1,
              width: 90,
              color: Colors.black,
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Are you sure you want to Cancel Order ?",
              style: CustomTextStyles.b2_1,
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin:
                          const EdgeInsets.symmetric(horizontal: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary2,
                        borderRadius:
                            BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "No",
                        style: CustomTextStyles.b3_primary,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      dentistOrderController.cancelOrder(
                          dentistOrderController.ordersDetailModel!.data!.id);
                      homeController.getDashboard();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.only(right: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius:
                            BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Yes",
                        style: CustomTextStyles.b3_1,
                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }


  Future cancelSheetWeb(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true, // User must tap button
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Container(
            // height: 280, // Slightly more than 262 for better spacing
            width: MediaQuery.of(context).size.width * 0.45, // Not full screen
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Cancel Order",
                  style: CustomTextStyles.h6,
                ),
                const SizedBox(height: AppSizes.spaceBtwList),
                Container(
                  width: 100,
                  height: 1.5,
                  color: Colors.black,),
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Are you sure you want to Cancel order ?",
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.b2_1,
                ),
                const SizedBox(height: AppSizes.defaultSpace),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary2,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "No",
                            style: CustomTextStyles.b3_primary,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: InkWell(
                        onTap: () async {
                          dentistOrderController.cancelOrder(
                              dentistOrderController.ordersDetailModel!.data!.id);
                          homeController.getDashboard();
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.only(right: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Yes, Cancel",
                            style: CustomTextStyles.b3_1,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: AppSizes.defaultSpace),
              ],
            ),
          ),
        );
      },
    );
  }


  String getOrgLabel(String? orgName) {
    if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supplier";
      default:
        return "Organization Name"; // Default fallback
    }
  }

  List<String> getAllImageUrls(){
    final orderImages = dentistOrderController.ordersDetailModel?.data?.orderImages ?? [];
    return orderImages.expand((img) => img.images).toList();
  }

  Widget buildUploadedImages() {
    final images = getAllImageUrls();

    final orderImages = dentistOrderController.ordersDetailModel?.data?.orderImages ?? [];

    // Flatten all image URLs from each orderImage
    final List<String> allImageUrls = orderImages
        .expand((imageGroup) => imageGroup.images) // `images` is a List<String>
        .toList();

    return allImageUrls.isNotEmpty
        ? GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 8,
      ),
      itemCount: allImageUrls.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: (){
            Get.to(() => FullScreenImageView(imagePath: allImageUrls[index] ));
          },

          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              allImageUrls[index],
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey[200],
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                    ),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(Icons.broken_image, color: Colors.red),
                );
              },
            ),

          ),
        );
      },
    )
        : const Text("No images uploaded", style: TextStyle(fontSize: 16));
  }

  Widget buildUploadedFiles() {
    final orderFiles = dentistOrderController.ordersDetailModel?.data?.orderFiles ?? [];

    // Flatten all file URLs from each orderFile
    final List<String> allFileUrls = orderFiles
        .expand((fileGroup) => fileGroup.files) // `files` is a List<String>
        .toList();

    return allFileUrls.isNotEmpty
        ? ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: allFileUrls.length,
            itemBuilder: (context, index) {
              final fileUrl = allFileUrls[index];
              final fileName = fileUrl.split('/').last;

              return ListTile(
                title: Text(fileName),
                trailing: IconButton(
                  icon: const Icon(Icons.download),
                  onPressed: () async {
                    final Uri uri = Uri.parse(fileUrl);
                    if (await canLaunchUrl(uri)) {
                      await launchUrl(uri, mode: LaunchMode.externalApplication);
                    } else {
                      Get.snackbar('Error', 'Could not launch $fileUrl',
                        backgroundColor: AppColors.primary3,
                        colorText: Colors.white,
                        duration: const Duration(seconds: 3),
                        snackPosition: SnackPosition.TOP,
                      );
                    }
                  },
                ),
              );
            },
          )
        : const Text("No files uploaded", style: TextStyle(fontSize: 16));
  }
}

class BillDetailRow extends StatelessWidget {
  final String label;
  final String value;

  BillDetailRow({
    super.key,
    required this.label,
    required this.value,
  });

  final DentistOrderController dentistOrderController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: CustomTextStyles.b6_3.copyWith(color: AppColors.darkGrey),
          ),
          Text(
            value,
            style: CustomTextStyles.b6_1.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}
