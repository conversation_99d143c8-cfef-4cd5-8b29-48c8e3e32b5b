import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/controllers/dentist_controllers/item_details_controller.dart';
import 'package:platix/controllers/dentist_controllers/store_controller.dart';
import 'package:platix/data/models/item_model.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/full_screen_image_view.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class ItemDetailsScreen extends StatelessWidget {
  final Item item;

  const ItemDetailsScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    final ItemDetailsController controller = Get.put(ItemDetailsController(item: item));
    final List<String> images = item.photoUrl;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: item.name,
        centerTitle: true,
        backgroundColor: AppColors.primary,
        leadingBack: true,
        textStyle: CustomTextStyles.b4_1.copyWith(color: Colors.white),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      height: MediaQuery.of(context).size.width > 750 ? 350 : 250,
                      width: double.infinity, // Full width
                      child: PageView.builder(
                        key: ValueKey(images.length),
                        controller: controller.pageController,
                        itemCount: images.length,
                        physics: const BouncingScrollPhysics(),
                        itemBuilder: (context, index) {
                          return GestureDetector(
                            onTap: () {
                              Get.to(() => FullScreenImageView(imagePath: images[index]));
                            },
                            child: CustomImageView(
                              imagePath: images[index],
                              width: double.infinity, // Full width
                              height: 200,
                              fit: BoxFit.cover, // Ensures proper fit
                            ),
                          );
                        },
                      ),
                    ),
                    if (images.length > 1)
                      Positioned(
                        left: 0,
                        child: IconButton(
                          icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                          onPressed: () {
                            controller.pageController.previousPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          },
                        ),
                      ),
                    if (images.length > 1)
                      Positioned(
                        right: 0,
                        child: IconButton(
                          icon: const Icon(Icons.arrow_forward_ios, color: Colors.white),
                          onPressed: () {
                            controller.pageController.nextPage(
                              duration: const Duration(milliseconds: 300),
                              curve: Curves.ease,
                            );
                          },
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 10),
                if (images.length > 1)
                  Center(
                    child: SmoothPageIndicator(
                      controller: controller.pageController,
                      count: images.length,
                      effect: const ScrollingDotsEffect(
                        activeDotScale: 1.5,
                        maxVisibleDots: 5,
                        dotHeight: 8,
                        dotWidth: 8,
                        activeDotColor: AppColors.primary,
                        dotColor: Colors.grey,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              item.name,
              style: CustomTextStyles.h4,
            ),
            const SizedBox(height: 8),
            Text(
              'By ${item.manufacturerName}',
              style: CustomTextStyles.b2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  'MRP: \u20B9${item.mrp.toStringAsFixed(2)}',
                  style: CustomTextStyles.b2.copyWith(decoration: TextDecoration.lineThrough),
                ),
                const SizedBox(width: 16),
                Text(
                  'Our Price: \u20B9${item.discountedPrice.toStringAsFixed(2)}',
                  style: CustomTextStyles.h6.copyWith(color: AppColors.primary),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'You save \u20B9${(item.mrp - item.discountedPrice).toStringAsFixed(2)}!',
              style: CustomTextStyles.b4.copyWith(color: Colors.green[700]),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Text(
              'Description',
              style: CustomTextStyles.h6,
            ),
            const SizedBox(height: 8),
            Text(
              item.description,
              style: CustomTextStyles.b4,
            ),
            const SizedBox(height: 16),
            Text(
              'Category: ${item.itemCategory}',
              style: CustomTextStyles.b4,
            ),
            const SizedBox(height: 8),
            Text(
              'Distributor: ${item.distributorName}',
              style: CustomTextStyles.b4,
            ),
            const SizedBox(height: 32),
            CustomElevatedButton(
              text: "Add to Cart",
              onPressed: () {
                final cartController = Get.find<CartController>();
                cartController.addToCart(item);
                Get.snackbar(
                  "Added to Cart",
                  "${item.name} has been added to your cart.",
                  backgroundColor: AppColors.primary4,
                  colorText: Colors.white,
                  duration: const Duration(seconds: 3),
                  snackPosition: SnackPosition.TOP,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
