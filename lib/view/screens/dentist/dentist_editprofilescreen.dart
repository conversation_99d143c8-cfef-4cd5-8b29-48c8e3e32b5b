import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:platix/data/models/dentist/dentist_organizations.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_bottombar.dart';
import 'package:platix/view/screens/dentist/location_search_screen.dart';
import '../../../api/data_store.dart';
import '../../../controllers/dentist_controllers/dentist_profile_controller.dart';
import '../../../controllers/owner/owner_create_order_controller.dart';
import '../../../data/models/user_model.dart';
import 'package:image/image.dart' as img;


class DentistEditProfilescreen extends StatefulWidget {
  String? screenName;
   DentistEditProfilescreen({super.key,this.screenName});

  @override
  State<DentistEditProfilescreen> createState() => _DentistEditProfilescreenState();
}

class _DentistEditProfilescreenState extends State<DentistEditProfilescreen> {

  final TextEditingController firstName = TextEditingController();
  final TextEditingController lastName = TextEditingController();
  final TextEditingController hospitalName = TextEditingController();
  final TextEditingController registrationId = TextEditingController();
  final TextEditingController address = TextEditingController();
  final TextEditingController mapLink= TextEditingController();
  final TextEditingController email = TextEditingController();
  final TextEditingController mobileNumber = TextEditingController();
  final TextEditingController whatsappNumber = TextEditingController();
  final TextEditingController upiId = TextEditingController();

  var selectedPrefix;
  var selectedType;

  bool isLoading = false;

  String? path;

  XFile? _pickedFile; // Store the picked file reference


  Uint8List? _selectedImage;
  String? hospitalErrorText;



  //final ProfileController controller = Get.find<ProfileController>();
  final ProfileController controller = Get.put(ProfileController());
  final TextEditingController searchController = TextEditingController();
  final OwnerCreateOrderController createController = Get.put(OwnerCreateOrderController());

  final GlobalKey _searchFieldKey = GlobalKey();
  @override
  void initState() {
    super.initState();
    _loadUserData();
    controller.fetchUserProfile();
    final ProfileController profileController = Get.find<ProfileController>();
    profileController.getDentistOrganizations();

    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus) {
        _scrollToSearchField();
      }
    });
  }

  void _scrollToSearchField() {
    final context = _searchFieldKey.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }


  @override
  void dispose() {
    _scrollController.dispose();

    super.dispose();
  }


  bool isFreelancer = false;
  bool isHospital = false;

  final ScrollController _scrollController = ScrollController();
  final FocusNode _searchFocusNode = FocusNode();



  void _loadUserData() {
    var storedUser = getData.read("userRecord");

    if (storedUser != null) {
      if (storedUser is String) {
        storedUser = jsonDecode(storedUser);
      }

      if (storedUser is Map<String, dynamic>) {
        UserRecord user = UserRecord.fromJson(storedUser);
        selectedPrefix = user.prefix ?? "";
        selectedType = user.type ?? "";
        firstName.text = user.firstName ?? "";
        lastName.text = user.lastName ?? "";
        hospitalName.text = user.organization?.name ?? "";

        registrationId.text = user.registrationId ?? "";
        address.text = user.address ?? "";

        mapLink.text = (user.googleMapLink ?? user.organization?.googlemaplink)??"";
        email.text = user.email ?? "";
        mobileNumber.text = user.mobileNo ?? "";
        controller.selectedOption.value = user.type ?? "";
        whatsappNumber.text = user.whatsappNo ?? "";
        isFreelancer=user.isFreelancer??false;
        isHospital = user.organization?.name?.isNotEmpty ??false;
        searchController.text = isHospital ? user.organization?.name ?? '' : '';
        upiId.text = user.upiId ?? "";

        // log("dddddddddddddddd");
        // log(user.organization?.name ?? "");

        // 🔥 Fetch profile image from storage
       // String? storedImagePath = getData.read("profileImage");
        String? storedImagePath = user.profileImage;
        getData.write("profileImage", storedImagePath); // 🔥 Store it in GetStorage


        log("🔍 Stored Image Path: $storedImagePath");

        if (storedImagePath != null && storedImagePath.isNotEmpty) {
          log("🧭 Trying to load image from: $storedImagePath");

          if (storedImagePath.startsWith("http")) {
            // ✅ Network image
            setState(() {
              _selectedImage = null; // You can load this later using Image.network in your UI
            });
            log("🌐 Detected network image URL.");
          } else {
            // ✅ Local file path
            File imageFile = File(storedImagePath);
            if (imageFile.existsSync()) {
              log("✅ Local image file found, loading...");
              setState(() {
                _selectedImage = imageFile.readAsBytesSync();
              });
            } else {
              log("❌ Local image file does not exist at: $storedImagePath");
            }
          }
        }

      }
    }
  }





  // Future<void> _openGallery(BuildContext context) async {
  //   final ImagePicker picker = ImagePicker();
  //   final XFile? pickedFile = await picker.pickImage(
  //     source: ImageSource.gallery,
  //     imageQuality: 80, // Reduce image size
  //   );
  //
  //   if (pickedFile != null) {
  //     File file = File(pickedFile.path);
  //
  //     if (!kIsWeb) {
  //       final Uint8List imageBytes = await file.readAsBytes();
  //       final img.Image? image = img.decodeImage(imageBytes);
  //       if (image != null) {
  //         final img.Image resizedImage = img.copyResize(image, width: 500);
  //         final File resizedFile = File(file.path)
  //           ..writeAsBytesSync(img.encodeJpg(resizedImage, quality: 80));
  //
  //         setState(() {
  //           _pickedFile = XFile(resizedFile.path);
  //           _selectedImage = resizedFile.readAsBytesSync();
  //         });
  //
  //         // ✅ Save local image path
  //         getData.write("profileImage", resizedFile.path);
  //       }
  //     } else {
  //       Uint8List bytes = await pickedFile.readAsBytes();
  //       setState(() {
  //         _selectedImage = bytes;
  //       });
  //     }
  //   }
  // }

  Future<void> _openGallery(BuildContext context) async {
    final ImagePicker picker = ImagePicker();
    final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery, imageQuality: 80);

    if (pickedFile != null) {
      // Load the picked image
      File file = File(pickedFile.path);
      log("📸 Picked Image Path: ${file.path}");

      // Read the image and resize it
      img.Image? image = img.decodeImage(await file.readAsBytes());
      if (image != null) {
        // Resize image to 500x500 pixels (or any other size as required)
        img.Image resizedImage = img.copyResize(image, width: 500, height: 500);

        // Save the resized image to a new file
        final resizedFile = File(pickedFile.path)..writeAsBytesSync(img.encodePng(resizedImage));

        // Update the path and store it
        setState(() {
          _pickedFile = XFile(resizedFile.path);  // Update pickedFile to point to the resized file
        });

        log("✅ Image resized and saved to: ${resizedFile.path}");

        // Save the resized image path to GetStorage
        getData.write("profileImage", resizedFile.path);
      }
    } else {
      log("No image selected.");
    }
  }

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();




  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileController>(); // Initialize controller
    //String? selectedOption;
    return Scaffold(
      appBar: const CustomAppBar(
        title: "Edit Profile"
        ,backgroundColor: AppColors.primary,
        textColor: AppColors.white,
        leadingBack: true,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: FocusScope.of(context).unfocus,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Center(
              child: Container(
                color: Colors.white,
                width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                child: Padding(
                  padding: const EdgeInsets.all(AppSizes.md),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: AppSizes.defaultSpace,),
                        // Profile Image Section
                        Center(
                          child: SizedBox(
                            height: 100,
                            width: 100,
                            child: Stack(
                              children: [
                                InkWell(
                                  onTap: () => _openGallery(context), // Open gallery on tap
                                  child: Center(
                                    child: Container(
                                      height: 100,
                                      width: 100,
                                      decoration: BoxDecoration(
                                        border: Border.all(color: AppColors.primary, width: 1.6),
                                        shape: BoxShape.circle,
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(80),
                                        child: Builder(
                                          builder: (_) {
                                            final profileImagePath = getData.read("profileImage");

                                            if (_selectedImage != null) {
                                              return Image.memory(
                                                _selectedImage!,
                                                fit: BoxFit.cover,
                                                key: const ValueKey("memory"),
                                              );
                                            } else if (profileImagePath != null && profileImagePath.isNotEmpty) {
                                              if (profileImagePath.startsWith("http")) {
                                                return Image.network(
                                                  profileImagePath,
                                                  fit: BoxFit.cover,
                                                  key: const ValueKey("network"),
                                                  loadingBuilder: (context, child, loadingProgress) {
                                                    if (loadingProgress == null) return child;
                                                    return const Center(child: CircularProgressIndicator());
                                                  },
                                                  errorBuilder: (_, __, ___) => Image.asset(AppImages.test, fit: BoxFit.cover),
                                                );
                                              } else {
                                                final file = File(profileImagePath);
                                                if (file.existsSync()) {
                                                  return Image.file(
                                                    file,
                                                    fit: BoxFit.cover,
                                                    key: const ValueKey("file"),
                                                  );
                                                }
                                              }
                                            }

                                            if (profileImagePath != null && profileImagePath.isNotEmpty) {
                                              if (profileImagePath.startsWith("http")) {
                                                // 🌐 Image from network
                                                return Image.network(
                                                  profileImagePath,
                                                  fit: BoxFit.cover,
                                                  key: const ValueKey("network"),
                                                  errorBuilder: (_, __, ___) =>
                                                      Image.asset(AppImages.test, fit: BoxFit.cover),
                                                );
                                              } else {
                                                // 🗂️ Image from local file
                                                final file = File(profileImagePath);
                                                if (file.existsSync()) {
                                                  return Image.file(
                                                    file,
                                                    fit: BoxFit.cover,
                                                    key: const ValueKey("file"),
                                                  );
                                                }
                                              }
                                            }

                                            // 🧾 Default fallback image
                                            return Image.asset(AppImages.test, fit: BoxFit.cover);
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                Positioned(
                                  bottom: 8,
                                  right: 2,
                                  child: InkWell(
                                    onTap: () => _openGallery(context),
                                    child: Container(
                                      height: 24,
                                      width: 24,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(color: AppColors.primary, width: 1.6),
                                      ),
                                      child: CircleAvatar(
                                        backgroundColor: Colors.white,
                                        child: CustomImageView(
                                          imagePath: AppIcons.add,
                                          color: Colors.purple,
                                          height: 16,
                                          width: 16,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: AppSizes.defaultSpace),
                        // Prefix Dropdown
                        const TextFieldLabel( label: 'Prefix',),
                        // CustomDropdown(
                        //   hintText: "Select",
                        //
                        //   items: const ["MR", "MRS", "MISS","MS"],
                        //   selectedValue: selectedPrefix,
                        //   onChanged: (value) {
                        //     changeSelectedOption1(value!);
                        //   },
                        // ),
                        CustomTextFormField(
                          allowShadow: true,
                          hintStyle: const TextStyle(
                              color: AppColors.black
                          ),
                          hintText: 'Dr.',
                          filled: false,
                          fillColor: AppColors.grey,
                          enabled: false,
                          borderDecoration: OutlineInputBorder(
                              borderSide: BorderSide.none,
                              borderRadius: BorderRadiusStyle.border12
                          ),
                        ),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'First Name', hint:"Enter First Name", controller: firstName, validator: AppValidators.validateText,),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'Last Name', hint:"Enter Last Name", controller: lastName, validator: AppValidators.validateText,),

                        const SizedBox(height: AppSizes.spaceBtwItems),

                        Row(
                          key: _searchFieldKey,
                          children: [
                            Checkbox(
                              value: isFreelancer,
                              onChanged: (value) {
                                setState(() {
                                  isFreelancer = value!;
                                });
                              },
                            ),
                            const Text("Freelancer"),
                            const SizedBox(width: 20),
                            Checkbox(
                              value: isHospital,
                              onChanged: (value) {
                                setState(() {
                                  isHospital = value!;
                                });
                              },
                            ),
                            const Text("Hospital Name"),
                          ],
                        ),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        // LabelTextField(
                        //   label: 'Hospital Name',
                        //   hint: "Enter Hospital Name",
                        //   controller: hospitalName,
                        //   validator: AppValidators.validateText,
                        //   isDisabled: !isHospital && !isFreelancer ? true : !isHospital,
                        // ),

                        const TextFieldLabel(label: "Hospital Name"),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: Container(
                                height: 54,
                                decoration: BoxDecoration(
                                  color: hospitalErrorText != null
                                      ? Colors.red.withOpacity(0.1)
                                      : AppColors.background1,
                                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                  border: Border.all(
                                    color: hospitalErrorText != null ? Colors.red : Colors.transparent,
                                    width: 1.5,
                                  ),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: AppColors.grey,
                                      offset: Offset(0, 1),
                                      blurRadius: 4,
                                    ),
                                  ],
                                ),
                                child: CustomSearchField<Datum>(
                                  focusNode: _searchFocusNode,
                                  hidePrefix: true,
                                  searchController: searchController,
                                  fetchItems: (String query) async {
                                    return await controller.searchHospitals(query);
                                  },
                                  hintText: "Search for Hospitals",
                                  itemAsString: (data) => "${data.name}, ${data.address})",
                                  onSelected: (hospitalData) {
                                    setState(() {
                                      hospitalErrorText = null; // clear error
                                    });
                                    searchController.text = hospitalData?.name ?? '';
                                  },
                                  onChanged: (query) {
                                    log("Search Query: $query");
                                    if (query.isNotEmpty) {
                                      setState(() {
                                        hospitalErrorText = null; // clear error while typing
                                      });
                                    }
                                  },
                                  isDisabled: !isHospital && !isFreelancer ? true : !isHospital,
                                ),
                              ),
                            ),
                            if (hospitalErrorText != null)
                              Padding(
                                padding: const EdgeInsets.only(left: 12.0, top: 4.0),
                                child: Text(
                                  hospitalErrorText!,
                                  style: const TextStyle(color: Colors.red, fontSize: 12),
                                ),
                              ),
                          ],
                        ),


                        const SizedBox(height: AppSizes.spaceBtwItems),

                        LabelTextField(
                          label: 'Registration ID',
                          hint: "Enter Registration ID",
                          controller: registrationId,
                          //validator: AppValidators.validatePhoneNumber,
                          isDisabled: !isHospital && !isFreelancer ? true : !isHospital,
                            validator: AppValidators.validateText
                        ),


                        const SizedBox(height: AppSizes.spaceBtwItems),
                        const TextFieldLabel(label: 'Address'),
                        CustomTextFormField(
                          controller: address,
                          hintText: 'Search for your location',
                          allowShadow: true,
                          textStyle: const TextStyle(color: AppColors.primary),
                          suffix: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: CustomImageView(
                              imagePath: AppIcons.location,
                              height: 20,
                              width: 20,
                            ),
                          ),
                          onTap: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => LocationSearchScreen(
                                  apiKey: "AIzaSyBpl77NqwIvWrtEjZwvFcQ0NqA2NPkOjf0",
                                ),
                              ),
                            );
                            if (result != null) {
                              setState(() {
                                address.text = result['address'];
                                mapLink.text =
                                    'https://www.google.com/maps/search/?api=1&query=${result['latlng'].latitude},${result['latlng'].longitude}';
                              });
                            }
                          },
                          validator: AppValidators.validateText,
                        ),
                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(
                          label: 'Google Map Link',
                          hint: "Enter Google Map Link",
                          controller: mapLink,
                          validator: AppValidators.validateText,
                          isDisabled: true,
                        ),


                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'Email', hint:"eg: <EMAIL>", controller: email, validator: AppValidators.validateEmail,),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'Mobile Number', hint:"eg: +91 9000000000", controller: mobileNumber,),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'Whatsapp Number', hint:"Enter Number", controller: whatsappNumber, validator: AppValidators.validateText,isMandatory: true,),

                        const SizedBox(height: AppSizes.spaceBtwItems),
                        LabelTextField(label: 'UPI Id', hint:"Enter UPI ID", controller: upiId, validator: AppValidators.validateText,),


                        const SizedBox(height: AppSizes.spaceBtwItems),
                        const TextFieldLabel(label: "Type of Dentist"),
                        CustomDropdown(
                         // buttonWidth: 380,
                          dropdownWidth: (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5)-30 ,
                          hintText: "Select",
                          items: const ["General Dentist","Pediatric Dentist", "Orthodontist ", "Endodontist","Periodontist","Prosthodontist "],
                          selectedValue:selectedType,
                          onChanged: (value) {
                            changeSelectedOption(value!);
                          },
                        ),
                        const SizedBox(height: AppSizes.defaultSpace),
                        // Update Button
                          CustomElevatedButton(
                            buttonStyle: const ButtonStyle(
                              backgroundColor: WidgetStatePropertyAll(AppColors.primary),
                            ),

                            text: isLoading?"Updating....":"Update",
                            onPressed: () async {
                              if (!_formKey.currentState!.validate()) {
                               // Get.snackbar("Validation Error", "Please fill all required fields correctly.");
                                return;
                              }

                              if (searchController.text.trim().isEmpty) {
                                setState(() {
                                  hospitalErrorText = "Hospital name is required";
                                });
                                return;
                              }

                              setState(() {
                                hospitalErrorText = null; // Clear if valid
                              });
                              Map<String, dynamic> updatedData = {


                                "firstName": firstName.text,
                                "lastName": lastName.text,
                                "hospital_name": (controller.dentistOrganizations?.any((data) => data.name == searchController.text.trim()) ?? false)? controller.dentistOrganizations?.firstWhere((data) => data.name == searchController.text.trim()).id : searchController.text,
                                "registrationId": registrationId.text,
                                "address": address.text,
                                "googleMapLink": mapLink.text,
                                "email": email.text,
                                "mobileNo": mobileNumber.text,
                                "type": selectedType,
                                "prefix":"DR",
                                "is_freelancer":isFreelancer,
                                "upiId":upiId.text,
                                "whatsappNo":whatsappNumber.text,

                              };
                              log("dfdfdfdfdfd");
                             print(updatedData);

                              // Check and log the image path if the file exists
                              if (_pickedFile != null) {
                                log("Selected image path: ${_pickedFile!.path}");
                              } else {
                                log("No image selected.");
                              }

                              bool isSuccess = await controller.updateUserProfile(
                                  updatedData,
                                  _pickedFile != null ? XFile(_pickedFile!.path) : null
                              );
                              log("🔴 Sending picked file: ${_pickedFile?.path}");

                              if (isSuccess) {
                                if (_pickedFile != null) {
                                  GetStorage().write("profileImage", _pickedFile!.path);
                                }
                                if (widget.screenName == "checkout") {
                                  Get.back(); // Navigate to Checkout Screen
                                } else {
                                  Get.off(() => const DentistBottomBar(initialIndex: 3,)); // Default: Navigate to Profile Screen
                                }
                              } else {
                                Get.snackbar("Error", "Update failed. Please try again.",
                                backgroundColor: AppColors.primary3,
                                colorText: Colors.white,
                                duration: const Duration(seconds: 3),
                                snackPosition: SnackPosition.TOP,
                                );
                              }
                            },
                          )

                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void changeSelectedOption(String value) {
    selectedType = value;
  }
  void changeSelectedOption1(String value) {
    selectedPrefix = value;
  }
}
