import 'package:platix/services/permission_service.dart';
import 'package:platix/view/widgets/enhanced_location_selector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:intl/intl.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_upsert_response_model.dart';
import 'package:platix/controllers/patient_registration_controller.dart';

class CreatePatientRegistrationScreen extends StatefulWidget {
  final PatientData? patientData; // For editing existing patient
  
  const CreatePatientRegistrationScreen({
    super.key,
    this.patientData,
  });

  @override
  State<CreatePatientRegistrationScreen> createState() =>
      _CreatePatientRegistrationScreenState();
}

class _CreatePatientRegistrationScreenState extends State<CreatePatientRegistrationScreen> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController mobileNumController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  String? _selectedGender;
  DateTime? _selectedDateOfBirth;
  String? selectedCountryId;
  String? selectedStateId;
  String? selectedCityName;
  final PermissionService _permissionService = PermissionService();
  final ApiService _apiService = ApiService();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFormData();
  }

  void _initializeFormData() {
    if (widget.patientData != null) {
      // Pre-fill form fields with existing patient data
      final patient = widget.patientData!;
      firstNameController.text = patient.firstName ?? '';
      lastNameController.text = patient.lastName ?? '';
      // Handle mobile number - extract digits only for display (remove +91 prefix)
      String mobile = patient.mobile ?? '';
      if (mobile.startsWith('+91')) {
        mobile = mobile.substring(3); // Remove +91 prefix for display
      }
      mobileNumController.text = mobile;
      emailController.text = patient.email ?? '';
      
      // Set gender - convert M/F to Male/Female
      if (patient.gender != null) {
        switch (patient.gender!.toUpperCase()) {
          case 'M':
          case 'MALE':
            _selectedGender = 'Male';
            break;
          case 'F':
          case 'FEMALE':
            _selectedGender = 'Female';
            break;
        }
      }
      
      // Set age if available
      if (patient.age != null) {
        ageController.text = patient.age!;
      }
      
      // Set address if available
      if (patient.address != null) {
        addressController.text = patient.address!;
      }
      
      // Set location data
      selectedCountryId = patient.country;
      selectedStateId = patient.state;
      selectedCityName = patient.city;
      
      // Parse date of birth if available
      if (patient.dob != null) {
        try {
          _selectedDateOfBirth = DateTime.parse(patient.dob!);
        } catch (e) {
          // If parsing fails, leave date empty
        }
      }
    }
  }

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileNumController.dispose();
    emailController.dispose();
    ageController.dispose();
    addressController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
        // Auto-calculate age when date of birth is selected
        final int calculatedAge = _calculateAge(picked);
        ageController.text = calculatedAge.toString();
      });
    }
  }

  /// Calculate age in years from date of birth
  int _calculateAge(DateTime birthDate) {
    final DateTime currentDate = DateTime.now();
    int age = currentDate.year - birthDate.year;
    final int monthDiff = currentDate.month - birthDate.month;
    
    // If the birth month hasn't occurred yet this year, or
    // if it's the birth month but the birth day hasn't occurred yet
    if (monthDiff < 0 || (monthDiff == 0 && currentDate.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  Future<void> _handlePatientRegistration({bool force = false}) async {
    // Validate required fields
    if (firstNameController.text.isEmpty ||
        lastNameController.text.isEmpty ||
        mobileNumController.text.isEmpty ||
        emailController.text.isEmpty) {
      Get.snackbar('Error', 'Please fill all required fields',
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
      return;
    }
    
    // Validate mobile number (should be exactly 10 digits)
    if (mobileNumController.text.length != 10 || !RegExp(r'^[0-9]+$').hasMatch(mobileNumController.text)) {
      Get.snackbar('Error', 'Please enter a valid 10-digit mobile number',
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
      return;
    }
    
    // Validate that age is provided (either manually or via DOB)
    if (ageController.text.isEmpty) {
      Get.snackbar('Error', 'Age is required. Please enter age or select date of birth.',
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
      return;
    }
    
    // Validate that age is a valid number
    final int? age = int.tryParse(ageController.text);
    if (age == null || age <= 0 || age > 150) {
      Get.snackbar('Error', 'Please enter a valid age between 1 and 150.',
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
      return;
    }

    // Validate gender selection
    if (_selectedGender == null) {
      Get.snackbar('Error', 'Please select gender.',
      backgroundColor: AppColors.primary3,
      colorText: Colors.white,
      duration: const Duration(seconds: 3),
      snackPosition: SnackPosition.TOP,
    );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      print('Starting patient registration API call...');
      // Prepare mobile number with +91 prefix for API call
      String mobileWithPrefix = '+91${mobileNumController.text}';
      
      final response = await _apiService.upsertPatient(
        id: widget.patientData?.id != null ? int.tryParse(widget.patientData!.id!) : null,
        firstName: firstNameController.text,
        lastName: lastNameController.text,
        mobile: mobileWithPrefix,
        email: emailController.text,
        age: ageController.text,
        gender: _selectedGender!, // Send full gender name (Male/Female)
        country: selectedCountryId,
        state: selectedStateId,
        city: selectedCityName,
        address: addressController.text.isNotEmpty ? addressController.text : null,
        dob: _selectedDateOfBirth?.toIso8601String(),
        force: force,
      );

      print('API Response received - Status: ${response.status}, Message: ${response.message}');
      print('Response object: $response');
      
      // Check response.status field (from API response body)
      if (response.status == true) {
        // Success case - patient created/updated successfully
        print('Success response - showing snackbar');
        Get.snackbar(
          'Success',
          response.message ?? 'Patient registration saved successfully',
          backgroundColor: AppColors.primary4,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        
        // Add a small delay before navigating back to ensure snackbar is shown
        await Future.delayed(const Duration(milliseconds: 500));
        
        // Try to refresh the patient list if controller exists
        try {
          final controller = Get.find<PatientRegistrationController>();
          controller.refreshData();
        } catch (e) {
          print('Controller not found: $e');
        }
        
        // Navigate back using Navigator.pop for more reliable navigation
        if (Navigator.canPop(context)) {
          Navigator.pop(context, true);
        } else {
          // Fallback to Get.back if Navigator.pop doesn't work
          Get.back(result: true);
        }
      } else if (response.status == false) {
        // Handle duplicate mobile number case or other business logic errors
        print('Response status is false - Message: ${response.message}');
        if (response.message != null && response.message!.toLowerCase().contains('already registered')) {
          _showDuplicateDialog();
        } else {
          Get.snackbar('Error', response.message ?? 'An error occurred',
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
            snackPosition: SnackPosition.TOP,
          );
        }
      } else {
        // Handle unexpected response structure
        print('Unexpected response status: ${response.status}');
        Get.snackbar('Error', 'Unexpected response from server',
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      print('Patient registration error: $e');
      print('Error type: ${e.runtimeType}');
      
      // Handle different types of errors
      if (e.toString().contains('DioException')) {
        Get.snackbar('Error', 'Network error. Please check your connection and try again.',
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      } else if (e.toString().contains('FormatException')) {
        Get.snackbar('Error', 'Invalid response format from server.',
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      } else {
        Get.snackbar('Error', 'Failed to save patient registration. Please try again.',
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showDuplicateDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('Patient Already Exists'),
        content: const Text(
          'A patient with this mobile number is already registered. Do you want to proceed anyway?'
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _handlePatientRegistration(force: true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            child: const Text('Proceed'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.patientData != null ? 'Edit Patient Registration' : 'New Patient Registration'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            LabelTextField(
              label: 'First Name',
              hint: 'Enter First Name',
              controller: firstNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Last Name',
              hint: 'Enter Last Name',
              controller: lastNameController,
            ),
            const SizedBox(height: 15),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Mobile Num',
                        style: CustomTextStyles.b4_1,
                      ),
                      TextSpan(
                        text: "*",
                        style: CustomTextStyles.b4Primary,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          bottomLeft: Radius.circular(8),
                        ),
                        color: Colors.grey.shade100,
                      ),
                      child: Text(
                        '+91',
                        style: CustomTextStyles.b4_1.copyWith(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Expanded(
                      child: TextFormField(
                        controller: mobileNumController,
                        keyboardType: TextInputType.phone,
                        maxLength: 10,
                        decoration: InputDecoration(
                          hintText: 'Enter 10-digit mobile number',
                          hintStyle: CustomTextStyles.b4_1.copyWith(color: Colors.grey.shade400),
                          border: OutlineInputBorder(
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                            borderSide: BorderSide(color: AppColors.primary, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          counterText: '', // Hide character counter
                        ),
                        onChanged: (value) {
                          // Remove any non-digit characters
                          String cleaned = value.replaceAll(RegExp(r'[^0-9]'), '');
                          if (cleaned.length <= 10) {
                            mobileNumController.value = TextEditingValue(
                              text: cleaned,
                              selection: TextSelection.collapsed(offset: cleaned.length),
                            );
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Email',
              hint: 'Enter Email',
              controller: emailController,
              inputType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 15),
            Text(
              'Gender',
              style: CustomTextStyles.b4_1,
            ),
            Row(
              children: [
                Radio<String>(
                  value: 'Male',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('Male'),
                const SizedBox(width: 20),
                Radio<String>(
                  value: 'Female',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const Text('Female'),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: LabelTextField(
                    label: 'Age',
                    hint: 'Enter Age',
                    controller: ageController,
                    inputType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context),
                    child: AbsorbPointer(
                      child: LabelTextField(
                        label: 'DOB.',
                        hint: _selectedDateOfBirth == null
                            ? 'Select Date'
                            : DateFormat('dd/MM/yyyy')
                                .format(_selectedDateOfBirth!),
                        controller: TextEditingController(
                            text: _selectedDateOfBirth == null
                                ? ''
                                : DateFormat('dd/MM/yyyy')
                                    .format(_selectedDateOfBirth!)),
                        suffix: const Icon(Icons.calendar_today),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Address',
              hint: 'Enter Address',
              controller: addressController,
              maxLines: 3,
            ),
            const SizedBox(height: 15),
            EnhancedLocationSelector(
              initialCountryId: selectedCountryId,
              initialStateId: selectedStateId,
              initialCityName: selectedCityName,
              onCountryChanged: (countryId, countryName) {
                setState(() {
                  selectedCountryId = countryId;
                  selectedStateId = null; // Reset state when country changes
                  selectedCityName = null; // Reset city when country changes
                });
              },
              onStateChanged: (stateId, stateName) {
                setState(() {
                  selectedStateId = stateId;
                  selectedCityName = null; // Reset city when state changes
                });
              },
              onCityChanged: (cityName) {
                setState(() {
                  selectedCityName = cityName;
                });
              },
            ),
            const SizedBox(height: 30),
            CustomElevatedButton(
              onPressed: _isLoading ? null : () => _handlePatientRegistration(),
              text: _isLoading
                ? 'Saving...'
                : (widget.patientData != null ? 'Update' : 'Save'),
              buttonStyle: ElevatedButton.styleFrom(
                backgroundColor: _isLoading ? Colors.grey : AppColors.primary,
                foregroundColor: AppColors.white,
              ),
            ),
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.only(top: 16.0),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
