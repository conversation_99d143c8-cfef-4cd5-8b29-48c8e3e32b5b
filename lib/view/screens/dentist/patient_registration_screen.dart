import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/patient_registration_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/create_patient_registration_screen.dart';
import 'package:platix/view/widgets/patient_card.dart';

class PatientRegistrationScreen extends StatelessWidget {
  const PatientRegistrationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    final PatientRegistrationController controller = Get.put(PatientRegistrationController());

    return Visibility(
      visible: permissionService.hasAnyPermission('Registration', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Patient Registration'),
        ),
        body: Column(
          children: [
            // Search and Filter Section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Patient Registration List',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  // Search Field
                  CustomTextFormField(
                    hintText: 'Search by name, email, mobile, or patient ID...',
                    controller: controller.searchController,
                    prefix: Container(
                      margin: const EdgeInsets.all(12),
                      child: const Icon(Icons.search),
                    ),
                    suffix: Obx(() => controller.searchQuery.value.isNotEmpty
                        ? Container(
                            margin: const EdgeInsets.all(8),
                            child: IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: controller.clearSearch,
                            ),
                          )
                        : const SizedBox.shrink()),
                    onChanged: controller.onSearchChanged,
                  ),
                  const SizedBox(height: 16),
                  // Stats Row
                  Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        controller.totalRecordsText,
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                      Text(
                        controller.paginationText,
                        style: const TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    ],
                  )),
                ],
              ),
            ),
            // Data Table Section
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value && !controller.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (controller.hasError.value && !controller.hasData) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, size: 64, color: Colors.red),
                        const SizedBox(height: 16),
                        Text(
                          'Error: ${controller.errorMessage.value}',
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: controller.refreshData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (!controller.hasData) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.people_outline, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'No patient registrations found',
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return Visibility(
                  visible: permissionService.hasPermission('Registration', 'is_list'),
                  child: RefreshIndicator(
                    onRefresh: controller.refreshData,
                    child: ListView.builder(
                      controller: controller.scrollController,
                      padding: const EdgeInsets.only(bottom: 80), // Space for FAB
                      itemCount: controller.patientRegistrations.length +
                                 (controller.isLoading.value && controller.hasData ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Loading indicator at the end
                        if (index >= controller.patientRegistrations.length) {
                          return Container(
                            padding: const EdgeInsets.all(20),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        final patient = controller.patientRegistrations[index];

                        return PatientCard(
                          patient: patient,
                          index: index,
                          permissionService: permissionService,
                          onDelete: () {
                            controller.deletePatient(
                              patient.id ?? '',
                              patient.displayName,
                            );
                          },
                        );
                      },
                    ),
                  ),
                );
              }),
            ),
            // Load More Button (if needed)
            Obx(() => controller.showLoadMore
                ? Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ElevatedButton(
                      onPressed: controller.loadNextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Load More'),
                    ),
                  )
                : const SizedBox.shrink()),
          ],
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('Registration', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreatePatientRegistrationScreen(
                patientData: null, // Explicitly null for new patient
              ));
            },
            backgroundColor: AppColors.primary,
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ),
      ),
    );
  }
}
