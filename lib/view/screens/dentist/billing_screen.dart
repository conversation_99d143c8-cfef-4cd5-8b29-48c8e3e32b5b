import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/create_billing_screen.dart';

class BillingScreen extends StatelessWidget {
  const BillingScreen({super.key});

  void _navigateToViewBilling({
    required String patientName,
    required String serviceName,
    required String amount,
    required String date,
  }) {
    Get.to(() => CreateBillingScreen(
      mode: BillingScreenMode.view,
      showSearchOption: false,
      patientName: patientName,
      serviceName: serviceName,
      amount: amount,
      date: date,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final PermissionService permissionService = PermissionService();
    return Visibility(
      visible: permissionService.hasAnyPermission('billing', ['is_view', 'is_list', 'is_add']),
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Billing'),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Patient Billing',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 20),
                Visibility(
                  visible: permissionService.hasPermission('billing', 'is_list'),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columnSpacing: 10,
                      horizontalMargin: 10,
                      headingRowColor: WidgetStateProperty.all(AppColors.primary),
                      headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      columns: const [
                        DataColumn(label: Text('S.No')),
                        DataColumn(label: Text('Patient Name')),
                        DataColumn(label: Text('Service')),
                        DataColumn(label: Text('Amount')),
                        DataColumn(label: Text('Date')),
                        DataColumn(label: Text('Edit')),
                        DataColumn(label: Text('Delete')),
                      ],
                      rows: [
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Matthew Thomas',
                                    serviceName: 'Root Canal',
                                    amount: '₹5,000',
                                    date: '30/10/2020',
                                  );
                                },
                                child: const Text('1'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Matthew Thomas',
                                    serviceName: 'Root Canal',
                                    amount: '₹5,000',
                                    date: '30/10/2020',
                                  );
                                },
                                child: const Text('Matthew Thomas'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Matthew Thomas',
                                    serviceName: 'Root Canal',
                                    amount: '₹5,000',
                                    date: '30/10/2020',
                                  );
                                },
                                child: const Text('Root Canal'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Matthew Thomas',
                                    serviceName: 'Root Canal',
                                    amount: '₹5,000',
                                    date: '30/10/2020',
                                  );
                                },
                                child: const Text('₹5,000'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Matthew Thomas',
                                    serviceName: 'Root Canal',
                                    amount: '₹5,000',
                                    date: '30/10/2020',
                                  );
                                },
                                child: const Text('30/10/2020'),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(
                                      mode: BillingScreenMode.edit,
                                      showSearchOption: false,
                                      patientName: 'Matthew Thomas',
                                      serviceName: 'Root Canal',
                                      amount: '₹5,000',
                                      date: '30/10/2020',
                                    ));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Matthew Thomas?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Matthew Thomas deleted successfully!',
                                          backgroundColor: AppColors.primary4,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                          snackPosition: SnackPosition.TOP,
                                        );
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'John Davis',
                                    serviceName: 'Dental Cleaning',
                                    amount: '₹2,500',
                                    date: '16/10/2021',
                                  );
                                },
                                child: const Text('2'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'John Davis',
                                    serviceName: 'Dental Cleaning',
                                    amount: '₹2,500',
                                    date: '16/10/2021',
                                  );
                                },
                                child: const Text('John Davis'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'John Davis',
                                    serviceName: 'Dental Cleaning',
                                    amount: '₹2,500',
                                    date: '16/10/2021',
                                  );
                                },
                                child: const Text('Dental Cleaning'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'John Davis',
                                    serviceName: 'Dental Cleaning',
                                    amount: '₹2,500',
                                    date: '16/10/2021',
                                  );
                                },
                                child: const Text('₹2,500'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'John Davis',
                                    serviceName: 'Dental Cleaning',
                                    amount: '₹2,500',
                                    date: '16/10/2021',
                                  );
                                },
                                child: const Text('16/10/2021'),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(
                                      mode: BillingScreenMode.edit,
                                      showSearchOption: false,
                                      patientName: 'John Davis',
                                      serviceName: 'Dental Cleaning',
                                      amount: '₹2,500',
                                      date: '16/10/2021',
                                    ));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for John Davis?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for John Davis deleted successfully!',
                                          backgroundColor: AppColors.primary4,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                          snackPosition: SnackPosition.TOP,
                                        );
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Elizabeth Jones',
                                    serviceName: 'Crown Placement',
                                    amount: '₹8,000',
                                    date: '10/01/2024',
                                  );
                                },
                                child: const Text('3'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Elizabeth Jones',
                                    serviceName: 'Crown Placement',
                                    amount: '₹8,000',
                                    date: '10/01/2024',
                                  );
                                },
                                child: const Text('Elizabeth Jones'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Elizabeth Jones',
                                    serviceName: 'Crown Placement',
                                    amount: '₹8,000',
                                    date: '10/01/2024',
                                  );
                                },
                                child: const Text('Crown Placement'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Elizabeth Jones',
                                    serviceName: 'Crown Placement',
                                    amount: '₹8,000',
                                    date: '10/01/2024',
                                  );
                                },
                                child: const Text('₹8,000'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Elizabeth Jones',
                                    serviceName: 'Crown Placement',
                                    amount: '₹8,000',
                                    date: '10/01/2024',
                                  );
                                },
                                child: const Text('10/01/2024'),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(
                                      mode: BillingScreenMode.edit,
                                      showSearchOption: false,
                                      patientName: 'Elizabeth Jones',
                                      serviceName: 'Crown Placement',
                                      amount: '₹8,000',
                                      date: '10/01/2024',
                                    ));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Elizabeth Jones?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Elizabeth Jones deleted successfully!',
                                          backgroundColor: AppColors.primary4,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                          snackPosition: SnackPosition.TOP,
                                        );
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(Colors.white),
                          cells: [
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Ryan Young',
                                    serviceName: 'Tooth Extraction',
                                    amount: '₹1,500',
                                    date: '09/01/2022',
                                  );
                                },
                                child: const Text('4'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Ryan Young',
                                    serviceName: 'Tooth Extraction',
                                    amount: '₹1,500',
                                    date: '09/01/2022',
                                  );
                                },
                                child: const Text('Ryan Young'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Ryan Young',
                                    serviceName: 'Tooth Extraction',
                                    amount: '₹1,500',
                                    date: '09/01/2022',
                                  );
                                },
                                child: const Text('Tooth Extraction'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Ryan Young',
                                    serviceName: 'Tooth Extraction',
                                    amount: '₹1,500',
                                    date: '09/01/2022',
                                  );
                                },
                                child: const Text('₹1,500'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Ryan Young',
                                    serviceName: 'Tooth Extraction',
                                    amount: '₹1,500',
                                    date: '09/01/2022',
                                  );
                                },
                                child: const Text('09/01/2022'),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(
                                      mode: BillingScreenMode.edit,
                                      showSearchOption: false,
                                      patientName: 'Ryan Young',
                                      serviceName: 'Tooth Extraction',
                                      amount: '₹1,500',
                                      date: '09/01/2022',
                                    ));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Ryan Young?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Ryan Young deleted successfully!',
                                          backgroundColor: AppColors.primary4,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                          snackPosition: SnackPosition.TOP,
                                        );
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                        DataRow(
                          color: WidgetStateProperty.all(AppColors.primary2),
                          cells: [
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Anthony Moore',
                                    serviceName: 'Dental Implant',
                                    amount: '₹15,000',
                                    date: '30/01/2025',
                                  );
                                },
                                child: const Text('5'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Anthony Moore',
                                    serviceName: 'Dental Implant',
                                    amount: '₹15,000',
                                    date: '30/01/2025',
                                  );
                                },
                                child: const Text('Anthony Moore'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Anthony Moore',
                                    serviceName: 'Dental Implant',
                                    amount: '₹15,000',
                                    date: '30/01/2025',
                                  );
                                },
                                child: const Text('Dental Implant'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Anthony Moore',
                                    serviceName: 'Dental Implant',
                                    amount: '₹15,000',
                                    date: '30/01/2025',
                                  );
                                },
                                child: const Text('₹15,000'),
                              ),
                            ),
                            DataCell(
                              InkWell(
                                onTap: () {
                                  _navigateToViewBilling(
                                    patientName: 'Anthony Moore',
                                    serviceName: 'Dental Implant',
                                    amount: '₹15,000',
                                    date: '30/01/2025',
                                  );
                                },
                                child: const Text('30/01/2025'),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_edit'),
                                child: IconButton(
                                  icon: const Icon(Icons.edit, color: AppColors.primary),
                                  onPressed: () {
                                    Get.to(() => const CreateBillingScreen(
                                      mode: BillingScreenMode.edit,
                                      showSearchOption: false,
                                      patientName: 'Anthony Moore',
                                      serviceName: 'Dental Implant',
                                      amount: '₹15,000',
                                      date: '30/01/2025',
                                    ));
                                  },
                                ),
                              ),
                            ),
                            DataCell(
                              Visibility(
                                visible: permissionService.hasPermission('billing', 'is_delete'),
                                child: IconButton(
                                  icon: const Icon(Icons.delete, color: AppColors.primary),
                                  onPressed: () {
                                    Get.defaultDialog(
                                      title: 'Delete Billing',
                                      titleStyle: const TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      middleText: 'Are you sure you want to delete billing for Anthony Moore?',
                                      middleTextStyle: const TextStyle(
                                        color: AppColors.black,
                                        fontSize: 14,
                                      ),
                                      backgroundColor: AppColors.white,
                                      radius: 12,
                                      textConfirm: 'Delete',
                                      textCancel: 'Cancel',
                                      confirmTextColor: Colors.white,
                                      cancelTextColor: AppColors.primary,
                                      buttonColor: AppColors.primary,
                                      onConfirm: () {
                                        Get.back();
                                        Get.snackbar('Success', 'Billing for Anthony Moore deleted successfully!',
                                          backgroundColor: AppColors.primary4,
                                          colorText: Colors.white,
                                          duration: const Duration(seconds: 3),
                                          snackPosition: SnackPosition.TOP,
                                        );
                                      },
                                      onCancel: () {
                                        Get.back();
                                      },
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Visibility(
          visible: permissionService.hasPermission('billing', 'is_add'),
          child: FloatingActionButton(
            onPressed: () {
              Get.to(() => const CreateBillingScreen(
                mode: BillingScreenMode.create,
                showSearchOption: true,
              ));
            },
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }
}
