import 'dart:developer';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/data/models/dentist/doctor_service_info.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/dentist_checkout_screen.dart';
import 'package:platix/view/widgets/multi_select_dropdown/core/multi_select.dart';

import '../../../controllers/dentist_controllers/dentist_service_controller.dart';
import '../../../data/models/dentist/dentist_home_data_model.dart';

class DentistLabCreateorderScreen extends StatefulWidget {
  const DentistLabCreateorderScreen({super.key});

  @override
  State<DentistLabCreateorderScreen> createState() => _DentistLabCreateorderScreenState();
}

class _DentistLabCreateorderScreenState extends State<DentistLabCreateorderScreen> {
  final DoctorServiceController doctorServiceController = Get.find();
  final _formKey = GlobalKey<FormState>();
  final PermissionService _permissionService = PermissionService();

  String? selectedLaboratory;
  List<dynamic> filteredServices = [];
  List<Choice<dynamic>> selectedServices = []; // Holds selected services

  String? selectedDentalLaboratoryId; // Store the selected Radiology ID

  bool _showServiceError = false;
  bool _orderDateError = false;
  bool _requiredDateError = false;
  bool _shadeError = false;
  bool _genderError = false;
  bool _toothError = false;
  bool _shadeGuideError = false;
  bool _impressionTypeError = false;
  bool _fileError = false;
  bool _laboratoryError = false;

  List<DoctorService>? services;
  String? selectedvalue; // To hold selected shade value
  String? selectedToothvalue;
  //List<DoctorService> selectedServices = [];

  TextEditingController patientNameController = TextEditingController();
  TextEditingController patientIdController = TextEditingController();
  TextEditingController ageController = TextEditingController();
  String? gender;
  String? fromTooth;
  String? toTooth;
  String? vitaShadeGuide;
  String? vita3DShade;
  String? impressionType;
  String? shadeType;
  TextEditingController laboratoryNameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController remarksController = TextEditingController();
  TextEditingController toothNameController = TextEditingController();
  //TextEditingController quantityController = TextEditingController();

  double totalPrice = 0.0;
  DateTime? orderDate;
  DateTime? requiredDate;
  List<File> _files = [];

  Future<void> _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.any,
    );

    if (result != null) {
      if (_files.length + result.files.length > 5) {
        Get.snackbar(
          'Limit Exceeded',
          'You can only upload a maximum of 5 files.',
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        return;
      }

      for (var pickedFile in result.files) {
        if (_files.any((f) => f.path.split('/').last == pickedFile.name)) {
          Get.snackbar(
            'File Already Selected',
            '${pickedFile.name} has already been selected.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }
        final file = File(pickedFile.path!);
        final extension = file.path.split('.').last.toLowerCase();
        if (extension != 'stl') {
          Get.snackbar(
            'Invalid File Type',
            '${pickedFile.name} is not a valid STL file.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
          continue;
        }

        if (file.lengthSync() > 10 * 1024 * 1024) {
          Get.snackbar(
            'File Too Large',
            '${pickedFile.name} exceeds the 10 MB size limit.',
            snackPosition: SnackPosition.TOP,
            backgroundColor: AppColors.primary3,
            colorText: Colors.white,
            duration: const Duration(seconds: 3),
          );
        } else {
          setState(() {
            _files.add(file);
          });
        }
      }
    } else {
      log('User canceled the picker');
    }
  }

  @override
  void initState() {
    super.initState();
    orderDate = DateTime.now();
    final arguments = Get.arguments;
    services = arguments['services'];
  }


  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected, String label, bool showError, {bool onlyToday = false, DateTime? minDate}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFieldLabel(label: label),
        GestureDetector(
          onTap: () => _selectDate(context, onDateSelected, onlyToday: onlyToday, minDate: minDate),
          child: Container(
            height: 56,
            width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  date != null ? DateFormat('dd-MM-yyyy').format(date) : "Select date",
                  style: date != null
                      ? CustomTextStyles.b2.copyWith(color: AppColors.black, fontWeight: FontWeight.w500)
                      : CustomTextStyles.b4Primary2.copyWith(color: AppColors.darkGrey),
                ),
                CustomImageView(imagePath: AppIcons.calender),
              ],
            ),
          ),
        ),
        if (showError)
          const Padding(
            padding: EdgeInsets.only(top: 8.0),
            child: Text(
              "This field is required",
              style: TextStyle(color: Colors.red, fontSize: 12),
            ),
          ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, Function(DateTime) onDateSelected, {bool onlyToday = false, DateTime? minDate}) async {
    final DateTime today = DateTime.now();
    final DateTime onlyTodayDate = DateTime(today.year, today.month, today.day);

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: onlyToday ? onlyTodayDate : (minDate ?? today),
      firstDate: onlyToday ? onlyTodayDate : (minDate ?? today),  // If onlyToday is true, restrict selection to today
      lastDate: onlyToday ? onlyTodayDate : DateTime(2100),  // If onlyToday is true, allow only today
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary, // Change the primary color
              onPrimary: Colors.white, // Text color on primary color
              onSurface: AppColors.black, // Text color on surface
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary, // Button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }


  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: _permissionService.hasPermission('dental_labs', 'is_view'),
      child: Scaffold(
        appBar: const CustomAppBar(
          title: 'Create Order',
          textColor: Colors.white,
          backgroundColor: AppColors.primary,
        ),
        body: GetBuilder<HomeController>(
          builder: (homeController) {
            final dentalData = homeController.dentistHomeDataModel?.dentalLaboratory;
            return SafeArea(
              child: GestureDetector(
                onTap: FocusScope.of(context).unfocus,
                child: SingleChildScrollView(
                  child: Form(
                    key: _formKey,
                    child: Center(
                      child: Container(
                        color: Colors.white,
                        width: MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(AppSizes.md),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const TextFieldLabel(
                                    label: 'Select organization',
                                  ),
                                  const SizedBox(height: AppSizes.spaceSmall,),
                                  CustomTextFormField(
                                    allowShadow: true,
                                    hintStyle: const TextStyle(
                                        color: AppColors.darkerGrey
                                    ),
                                    hintText: 'Laboratory',
                                    filled: true,
                                    fillColor: AppColors.grey,
                                    enabled: false,
                                    borderDecoration: OutlineInputBorder(
                                        borderSide: BorderSide.none,
                                        borderRadius: BorderRadiusStyle.border12
                                    ),
                                  ),
                                  const SizedBox(
                                    height: AppSizes.defaultSpace,
                                  ),
                                  LabelTextField(
                                    label: 'Patient name',
                                    hint: 'Enter Patient Name',
                                    controller: patientNameController,
                                    isMandatory: true,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Patient name is required';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  LabelTextField(
                                    label: 'Patient ID',
                                    hint: 'Enter Patient ID',
                                    controller: patientIdController,
                                    isMandatory: false,
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  LabelTextField(
                                    label: 'Age',
                                    hint: 'Enter Age',
                                    controller: ageController,
                                    isMandatory: true,
                                    inputType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Age is required';
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Gender'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Male',
                                        groupValue: gender,
                                        onChanged: (value) {
                                          setState(() {
                                            gender = value;
                                          });
                                        },
                                      ),
                                      const Text('Male'),
                                      Radio<String>(
                                        value: 'Female',
                                        groupValue: gender,
                                        onChanged: (value) {
                                          setState(() {
                                            gender = value;
                                            _genderError = false;
                                          });
                                        },
                                      ),
                                      const Text('Female'),
                                    ],
                                  ),
                                  if (_genderError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Gender is required",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  Visibility(
                                    visible: _permissionService.hasPermission('dental_labs', 'is_list'),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        const TextFieldLabel(
                                          label: 'Laboratory Name',
                                        ),
                                        const SizedBox(height: AppSizes.spaceSmall,),
                                        CustomDropdown(
                                          dropdownWidth: (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5)-30 ,
                                          hintText: 'Select Laboratory name',
                                          // items: dentalData != null ? dentalData.map((lab) => lab.name).toList() : [],
                                          items:dentalData != null
                                              ? dentalData.where((lab) => lab.isRaiseOrders == true).map((lab) => lab.name).toList(): [],
                                          onChanged: (String? value) {
                                            setState(() {
                                              selectedLaboratory = value;
                                              _laboratoryError = false;

                                              // Find the selected laboratory and get its services
                                              final selectedLab = dentalData?.firstWhere(
                                                    (lab) => lab.name == value,


                                                orElse: () => DentalLaboratory(
                                                  id: '',
                                                  name: '',
                                                  address: [],
                                                  organizationTypeId: '',
                                                  file1: '',
                                                  organizationTypeName: '',
                                                  description: '',
                                                  services: [],
                                                  isRaiseOrders: false,
                                                ),
                                              );

                                              selectedDentalLaboratoryId = selectedLab?.id;

                                              filteredServices = selectedLab?.services ?? [];
                                            });
                                          },
                                        ),
                                        if (_laboratoryError)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 8.0),
                                            child: Text(
                                              "Laboratory name is required",
                                              style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                            ),
                                          ),

                                        const SizedBox(height: AppSizes.defaultSpace),
                                        const TextFieldLabel(label: 'Service Name'),
                                        CustomDropdown(
                                          dropdownWidth: (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.8  :  Get.size.width * 0.5)-30 ,
                                          hintText: 'Select Service',
                                          items: filteredServices.map((service) => service.servicename as String).toList(),
                                          onChanged: (value) {
                                            setState(() {
                                              final selectedService = filteredServices.firstWhere((s) => s.servicename == value);
                                              selectedServices = [Choice<dynamic>(selectedService.id.toString(), selectedService.servicename, metadata: selectedService)];
                                              _showServiceError = false;
                                            });
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (_showServiceError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Select a service",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Select Tooth'),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'From',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          onChanged: (value) {
                                            setState(() {
                                              fromTooth = value;
                                              _toothError = false;
                                            });
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: AppSizes.spaceSmall),
                                      Expanded(
                                        child: CustomDropdown(
                                          hintText: 'To',
                                          items: List.generate(38, (index) => (index + 11).toString()),
                                          onChanged: (value) {
                                            setState(() {
                                              toTooth = value;
                                              _toothError = false;
                                            });
                                          },
                                          selectedValue: toTooth,
                                        ),
                                      ),
                                    ],
                                  ),
                                  if (_toothError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select both 'From' and 'To' tooth",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Shades'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Vita shade guide',
                                        groupValue: shadeType,
                                        onChanged: (value) {
                                          setState(() {
                                            shadeType = value;
                                            selectedvalue = null;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita shade guide'),
                                      Radio<String>(
                                        value: 'Vita 3D master shade',
                                        groupValue: shadeType,
                                        onChanged: (value) {
                                          setState(() {
                                            shadeType = value;
                                            selectedvalue = null;
                                            _shadeGuideError = false;
                                          });
                                        },
                                      ),
                                      const Text('Vita 3D master shade'),
                                    ],
                                  ),
                                  if (_shadeGuideError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select a shade guide",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  if (shadeType == 'Vita shade guide')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        'A1 (Reddishbrown)', 'A2', 'A3', 'A4',
                                        'B1 (Reddish yellow)', 'B2', 'B3', 'B4',
                                        'C1 (Grey)', 'C2', 'C3', 'C4 (Reddish grey)',
                                        'D2', 'D3', 'D4'
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          selectedvalue = value;
                                          _shadeError = false;
                                        });
                                      },
                                      selectedValue: selectedvalue,
                                    ),
                                  if (shadeType == 'Vita 3D master shade')
                                    CustomDropdown(
                                      hintText: 'Select Shade',
                                      items: const [
                                        '1M-1', '1M-2', '2M-1', '2M-2', '2M-3',
                                        '2L-1.5', '2L-2.5', '2R-1.5', '2R-2.5',
                                        '3L-1.5', '3L-2.5', '3M-1', '3M-2', '3M-3',
                                        '3R-1.5', '3R-2.5', '4L-1.5', '4L-2.5',
                                        '4M-1', '4M-2', '4M-3', '4R-1.5', '4R-2.5',
                                        '5M-1', '5M-2', '5M-3'
                                      ],
                                      onChanged: (value) {
                                        setState(() {
                                          selectedvalue = value;
                                          _shadeError = false;
                                        });
                                      },
                                      selectedValue: selectedvalue,
                                    ),
                                  if (_shadeError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Shade is required",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Type of Impression'),
                                  Row(
                                    children: [
                                      Radio<String>(
                                        value: 'Physical',
                                        groupValue: impressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            impressionType = value;
                                            _impressionTypeError = false;
                                          });
                                        },
                                      ),
                                      const Text('Physical'),
                                      Radio<String>(
                                        value: 'Digital',
                                        groupValue: impressionType,
                                        onChanged: (value) {
                                          setState(() {
                                            impressionType = value;
                                            _impressionTypeError = false;
                                          });
                                        },
                                      ),
                                      const Text('Digital'),
                                    ],
                                  ),
                                  if (_impressionTypeError)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Please select the type of impression",
                                        style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                      ),
                                    ),
                                  if (impressionType == 'Digital')
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        CustomTextFormField(
                                          hintText: _files.isEmpty ? 'No file chosen' : '${_files.length} file(s) selected',
                                          suffix: Padding(
                                            padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: 11),
                                            child: CustomElevatedButton(
                                              height: 34,
                                              width: 100,
                                              text: 'Choose',
                                              onPressed: _pickFiles,
                                              buttonStyle: ButtonStyle(shape: WidgetStatePropertyAll(RoundedRectangleBorder(borderRadius: BorderRadiusStyle.radius8)), padding: const WidgetStatePropertyAll(EdgeInsets.only(right: AppSizes.xs))),
                                            ),
                                          ),
                                          validator: (value) {
                                            if (_files.isEmpty) {
                                              return 'Please upload a file';
                                            }
                                            for (var file in _files) {
                                              if (file.path.split('.').last.toLowerCase() != 'stl') {
                                                return 'Please upload a valid STL file';
                                              }
                                              if (file.lengthSync() > 10 * 1024 * 1024) {
                                                return 'File size should not exceed 10 MB';
                                              }
                                            }
                                            return null;
                                          },
                                        ),
                                        if (_fileError)
                                          Padding(
                                            padding: const EdgeInsets.only(top: 8.0),
                                            child: Text(
                                              "Please upload at least one STL file",
                                              style: CustomTextStyles.b6.copyWith(color: Colors.red),
                                            ),
                                          ),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        ..._files.map((file) => Row(
                                          children: [
                                            Expanded(child: Text(file.path.split('/').last)),
                                            IconButton(
                                              icon: const Icon(Icons.remove_circle),
                                              onPressed: () {
                                                setState(() {
                                                  _files.remove(file);
                                                });
                                              },
                                            ),
                                          ],
                                        )),
                                        const SizedBox(height: AppSizes.spaceSmall),
                                        const Text('(Upload up to 5 STL digital impression files)'),
                                      ],
                                    ),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  _buildDateField(orderDate, (date) {
                                    setState(() {
                                      orderDate = date;
                                      _orderDateError = false;
                                    });
                                  },
                                      'Order Date', _orderDateError),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  _buildDateField(requiredDate, (date) {
                                    requiredDate = date;
                                    _requiredDateError = false;

                                  }, 'Required Date', _requiredDateError, minDate: orderDate),
                                  const SizedBox(height: AppSizes.defaultSpace),
                                  const TextFieldLabel(label: 'Remarks'),
                                  CustomTextFormField(
                                    maxLines: 3,
                                    hintText: 'Write Note...',
                                    controller: remarksController,
                                    validator: (value) {
                                      if (value != null && value.length < 5) {
                                        return "Remarks must be at least 5 characters.";
                                      }
                                      return null;
                                    },
                                  ),
                                  const SizedBox(height: AppSizes.imageThumbSize),

                                  Visibility(
                                    visible: _permissionService.hasPermission('dental_labs', 'is_add'),
                                    child: CustomElevatedButton(
                                      text: 'Continue',
                                      onPressed: () {
                                        setState(() {
                                          _orderDateError = orderDate == null;
                                          _requiredDateError = requiredDate == null;
                                          _laboratoryError = selectedLaboratory == null;
                                          _showServiceError = selectedServices.isEmpty;
                                          _genderError = gender == null;
                                          _toothError = fromTooth == null || toTooth == null;
                                          _shadeGuideError = shadeType == null;
                                          _shadeError = selectedvalue == null || selectedvalue!.isEmpty;
                                          _impressionTypeError = impressionType == null;
                                          _fileError = impressionType == 'Digital' && _files.isEmpty;
                                        });

                                        if (_formKey.currentState!.validate() &&
                                            !_laboratoryError &&
                                            !_showServiceError &&
                                            !_orderDateError &&
                                            !_requiredDateError &&
                                            !_genderError &&
                                            !_toothError &&
                                            !_shadeGuideError &&
                                            !_shadeError &&
                                            !_impressionTypeError &&
                                            !_fileError) {

                                          List<Map<String, dynamic>> selectedServiceDetails = selectedServices.map((choice) {
                                            return {
                                              'id': choice.metadata?.id,  // Pass Service ID
                                              'name': choice.metadata?.servicename, // Pass Service Name
                                              'price': choice.metadata?.price, // Pass Service Price
                                              'quantity':1,
                                            };
                                          }).toList();
                                          log(selectedLaboratory!);
                                          Map<String, dynamic> orderData = {
                                            'patientName': patientNameController.text,
                                            'patientId': patientIdController.text,
                                            'age': ageController.text,
                                            'gender': gender,
                                            'laboratoryName': selectedLaboratory ?? '',
                                            // 'fromTooth': fromTooth,
                                            // 'toTooth': toTooth,
                                            'toothMappings': fromTooth != null && toTooth != null ? [{'from_tooth': fromTooth, 'to_tooth': toTooth, 'tooth_order': 1}] : [],
                                            'shade_type': shadeType,
                                            'shade': selectedvalue,
                                            'impression_type': impressionType,
                                            'stl_files': _files,
                                            'selectedServices': selectedServiceDetails,
                                            'orderDate': orderDate != null ? DateFormat('yyyy-MM-dd').format(orderDate!) : '',
                                            'requiredDate': requiredDate != null ? DateFormat('yyyy-MM-dd').format(requiredDate!) : '',
                                            'remarks': remarksController.text,
                                            'selected_toOrgId': selectedDentalLaboratoryId,
                                          };
                                          log('Order Data from create order screen: $orderData');

                                          Get.to(() => const DentalPlaceOrderScreen(serviceName: 'Dental Laboratory',), arguments: orderData);
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }
        ),
      ),
    );
  }
}
