
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/controllers/delivery_boy/delivery_create_order_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_order_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_service_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/deliveryboy/delivery_edit_order_success_screen.dart';
import '../../../api/data_store.dart';
import '../../../controllers/delivery_boy/delivery_orders_controller.dart';
import '../../../controllers/signin_controller.dart';
import '../../../data/models/delivery_boy/delivery_order_details_model.dart';
import '../../../data/models/user_model.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';


class DeliveryEditOrderScreen extends StatefulWidget {
  String? toOrgName;

   DeliveryEditOrderScreen({super.key,this.toOrgName});

  @override
  State<DeliveryEditOrderScreen> createState() =>
      _DeliveryEditOrderScreenState();
}

class _DeliveryEditOrderScreenState extends State<DeliveryEditOrderScreen> {
  final DeliveryOrderController deliveryOrderController = Get.find();
  final DeliveryServiceController deliveryServiceController = Get.find();
  final _formKey = GlobalKey<FormState>();
  final DeliveryOrdersController deliveryOrdersController = Get.put(DeliveryOrdersController());
  final DeliveryCreateOrderController editController = Get.put(DeliveryCreateOrderController());
  final SignInController signInController = Get.put(SignInController());


  // Text Controllers
  late TextEditingController doctorNameController;
  late TextEditingController hospitalNameController;
  late TextEditingController patientNameController;
  late TextEditingController patientIdController;
  late TextEditingController laboratoryController;
  late List<TextEditingController> serviceController;
  late TextEditingController orderDateController;
  late TextEditingController requiredDateController;
  late TextEditingController toothNameController;
  late List<TextEditingController> shadeController;
  late TextEditingController remarkController;
 // List<DeliveryService>? service;
  List<Orgservice>? selectedServices;
  bool showServicesError = false;


  List<String> items = ['Light', 'Medium', 'Dark'];

  DateTime? orderDate;
  DateTime? requiredDate;

  String? selectedvalue;
  String? selectedShade;
  //List<String> selectedServices = [];
   List<Choice<OrganizationService>> selectedChoices = [];


  late String hospitalname;
  late String doctorname;
  late String patientname;
  late String laboratoryname;
  late String toothname;
  late String remark;
  String? selectedToothValue;
  final userRecord = UserRecord.fromJson(getData.read('userRecord'));
  double subTotal = 0.0;
  double tax = 0.0;
  double totalAmount = 0.0;

  void recalculateTotal() {
    subTotal = 0.0;
    final arguments = Get.arguments;


    for (var service in selectedChoices) {
      double price = double.tryParse(service.metadata!.price.toString()) ?? 0.0;
      int quantity = arguments['quantity'] ?? 1;
      subTotal += price * quantity;
    }

    tax = subTotal * 0.18; // Assuming 18% GST
    totalAmount = subTotal + tax;

    setState(() {}); // to reflect the updated totals in UI if needed
  }




  @override
  void initState() {
    super.initState();
   // print("📅 Required Date: ${requiredDateController.text}");



    final arguments = Get.arguments;
    hospitalname = arguments["HospitalName"];
    doctorname = arguments["DoctorName"];
    patientname = arguments["PatientName"];
    laboratoryname = arguments["LaboratoryName"];
    orderDate = arguments["OrderDate"];
    requiredDate = arguments["RequiredDate"];
    selectedToothValue = arguments["ToothName"];
    selectedShade = arguments["Shade"];
    remark = arguments["remarks"];

    // final List<dynamic>? orderServicesRaw = Get.arguments?["Service"];
    //
    // if (orderServicesRaw != null) {
    //   selectedServices = orderServicesRaw.map((orderService) {
    //     if (orderService is OrderService) {
    //       return orderService.orgservice; // ✅ Directly return OrgService object
    //     } else if (orderService is Map<String, dynamic>) {
    //       return Orgservice.fromJson(orderService); // ✅ Convert Map to OrgService
    //     }
    //     return null;
    //   }).whereType<Orgservice>().toList();
    //
    //   print("✅ Selected Services from Order: ${selectedServices?.map((s) => s.servicess.servicename).toList()}");
    // }

    // ✅ Step 1: Get the passed services
    final List<dynamic>? orderServicesRaw = Get.arguments?["Service"];
    List<Orgservice>? service;

    if (orderServicesRaw != null) {
      service = orderServicesRaw.map((orderService) {
        if (orderService is OrderService) {
          return orderService.orgservice;
        } else if (orderService is Map<String, dynamic>) {
          return Orgservice.fromJson(orderService);
        }
        return null;
      }).whereType<Orgservice>().toList();

      print("✅ Step 1: Extracted Orgservice list:");
      service.forEach((s) {
        print("- Orgservice ID: ${s.id}, Name: ${s.servicess.servicename}");
      });
    }

    // ✅ Step 2: Get selected service IDs
    final selectedServiceIds = service?.map((s) => s.id.toString()).toList() ?? [];
    print("✅ Step 2: Selected service IDs: $selectedServiceIds");

    // ✅ Step 3: Get all available services for dropdown
    final allServices = userRecord.organization?.organizationService ?? [];
    print("✅ Step 3: Total services available: ${allServices.length}");
    for (var s in allServices) {
      print("- Available ID: ${s.id}, Name: ${s.servicess?.servicename}");
    }

    // ✅ Step 4: Build dropdown choices
    final dropdownChoices = allServices.map<Choice<OrganizationService>>((service) {
      return Choice<OrganizationService>(
        service.id.toString(),
        service.servicess?.servicename ?? '',
        metadata: service,
      );
    }).toList();

    print("✅ Step 4: Built dropdown choices: ${dropdownChoices.length}");

    selectedChoices = dropdownChoices
        .where((choice) {
      // Log both selectedServiceIds and choice.value to check for any mismatch
      print("✅ Comparing selectedServiceIds: $selectedServiceIds with choice value: ${choice.key}");


      // Normalize the IDs by trimming spaces
      final isMatch = selectedServiceIds.any((id) =>
      id.trim() == choice.key); // Compare IDs with trimming

      if (isMatch) {
        print("✅ Match found: ${choice.key} is in selectedServiceIds");
      } else {
        print("❌ No match: ${choice.key} is NOT in selectedServiceIds");

      }
      return isMatch;
    })
        .toList();

   // Log the filtered selected choices
    print("✅ Step 5: Filtered selected dropdown choices:");
    selectedChoices.forEach((c) {
      print("- Preselected Choice ID: ${c.value}, Label: ${c.key}");
      print(c.metadata?.price);
    });




    // Get the order ID passed from previous screen

    // Find the specific order
    // _currentOrder = deliveryOrderController
    //     .orderList
    //     .firstWhere((order) => order.orderId == orderId);

    // Initialize controllers with current order data

    doctorNameController = TextEditingController(text: doctorname);
    hospitalNameController = TextEditingController(text: hospitalname);
    patientNameController = TextEditingController(text: patientname);
    // patientIdController = TextEditingController(text: _currentOrder.patientId);
    laboratoryController = TextEditingController(text: laboratoryname);

    orderDateController = TextEditingController(text: formatDateString(orderDate.toString()));
    requiredDateController = TextEditingController(text: formatDateString(requiredDate.toString()));
    toothNameController = TextEditingController(text: selectedToothValue);

    remarkController = TextEditingController(text: remark);
    // shadeController = TextEditingController(text: selectedShade);
    // selectedShade = _currentOrder.shade;
    // service = _currentOrder.service;

    // Initialize service controller by joining all service names with space
    //  serviceController = TextEditingController(text: _currentOrder.service.map((service) => service.name).join(' ')) as List<TextEditingController>;
  }
  String formatDateString(String? dateStr) {
    DateTime parsedDate = DateTime.parse(dateStr!);
    return DateFormat('dd-MM-yyyy').format(parsedDate);
  }

  Future<void> _selectDate(
      BuildContext context, Function(DateTime) onDateSelected) async {
    DateTime initialDate = DateTime.now();
    if (orderDateController.text.isNotEmpty) {
      // If a date is already set, parse it
      List<String> dateParts = orderDateController.text.split('-');
      initialDate = DateTime(int.parse(dateParts[2]), int.parse(dateParts[1]),
          int.parse(dateParts[0]));
    }

    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  // void _saveChanges() {
  //   if (_formKey.currentState!.validate()) {
  //     // Update the order details
  //     setState(() {
  //       _currentOrder.doctorName = doctorNameController.text;
  //       _currentOrder.hospitalName = hospitalNameController.text;
  //       _currentOrder.patientName = patientNameController.text;
  //       _currentOrder.laboratoryName = laboratoryController.text;
  //       _currentOrder.toothName = toothNameController.text;
  //       _currentOrder.orderDate = orderDateController.text;
  //       _currentOrder.patientId = patientIdController.text;
  //       _currentOrder.patientName = patientNameController.text;
  //     });
  //
  //     // Pass the updated order back to the previous screen
  //     Get.back(result: _currentOrder); // Return updated data
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    final Map<String, dynamic> orderData = Get.arguments ?? {};
    print("📅 Required Date: ${requiredDateController.text}");
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Edit Order',
        backgroundColor: AppColors.primary,
        textColor: Colors.white,
      ),
      body: SafeArea(
        child: GestureDetector(
          onTap: FocusScope.of(context).unfocus,
          child: SingleChildScrollView(
            child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(AppSizes.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: AppSizes.sm,
                          ),

                          LabelTextField(
                            label: 'Name Of Doctor',
                            hint: 'Enter Doctor Name',
                            isDisabled: true,
                            controller: doctorNameController,
                            validator: AppValidators.validateText,
                          ),

                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            label: 'Name Of Hospital',
                            hint: 'Enter Hospital Name',
                            isDisabled: true,
                            controller: hospitalNameController,
                            validator: AppValidators.validateText,
                          ),

                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),
                          if (userRecord.organization?.organizationType?.organizationType == 'Dental Laboratory')...[
                          LabelTextField(
                            label: 'Patient Name',
                            hint: 'Enter Patient Name',
                            controller: patientNameController,
                            validator: AppValidators.validateText,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          )],

                          LabelTextField(
                            label: getOrgLabel(userRecord.organization?.organizationType?.organizationType),
                            hint: 'Enter Laboratory Name',
                            controller: laboratoryController,
                            validator: AppValidators.validateText,
                            isDisabled: true,
                          ),

                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          TextFieldLabel(label: userRecord.type == 'Material Supplier' ? 'Materials' : 'Services'),

                          MultiSelectField<OrganizationService>(
                            label: "Select Services",
                            textStyleLabel: CustomTextStyles.b4_1.copyWith(color: AppColors.darkGrey),

                            data: () {
                              final services = userRecord.organization?.organizationService;
                              return services?.map<Choice<OrganizationService>>((service) => Choice<OrganizationService>(
                                service.id.toString(),
                                service.servicess?.servicename ?? '',
                                metadata: service,
                              )).toList() ?? [];
                            },

                            onSelect: (List<Choice<OrganizationService?>> selected, bool _) {
                              setState(() {
                                selectedChoices = selected.cast<Choice<OrganizationService>>();
                              });
                              showServicesError = false;

                              selectedChoices.forEach((c) {
                                print("🔍 Selected Choice:");
                                print("- key: ${c.key}");
                                print("- value (ID): ${c.value}");
                                print("- metadata: ${c.metadata}");
                              });
                            },

                            defaultData: selectedChoices,

                            cleanCurrentSelection: deliveryServiceController.cleanCurrentSelection.value,  // or false
                            singleSelection: false,
                            useTextFilter: true,
                            menuStyle: MenuStyle(
                              shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8))),
                              backgroundColor: const WidgetStatePropertyAll(Colors.white),
                            ),
                            itemColor: ItemColor(
                                selected: Colors.purple.withOpacity(0.2),
                                unSelected: Colors.white
                            ),
                            itemMenuStyle: CustomTextStyles.b4_1,
                            isDisabled: false,
                          ),

                          // Wrap(
                          //   spacing: 8.0,
                          //   runSpacing: 8.0,
                          //   children: selectedServices!.map((service) {
                          //     return Chip(
                          //       label: Text(
                          //         service.servicess.servicename,
                          //         style: CustomTextStyles.b4_1.copyWith(color: AppColors.black),
                          //       ),
                          //       deleteIcon: const Icon(Icons.close, size: 18, color: AppColors.black),
                          //       onDeleted: () {
                          //         if (selectedServices?.length == 1) {
                          //           // Show error message if only one service is left
                          //           ScaffoldMessenger.of(context).showSnackBar(
                          //             const SnackBar(
                          //               content: Text("You must have at least one selected service!"),
                          //               backgroundColor: Colors.red,
                          //               duration: Duration(seconds: 2),
                          //             ),
                          //           );
                          //         } else {
                          //           setState(() {
                          //             selectedServices?.remove(service);
                          //             recalculateTotal();
                          //           });
                          //         }
                          //       },
                          //       shape: RoundedRectangleBorder(
                          //         borderRadius: BorderRadius.circular(6),
                          //       ),
                          //       backgroundColor: Colors.purple.withOpacity(0.2),
                          //     );
                          //   }).toList(),
                          // ),



                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          const TextFieldLabel(label: 'Order Date'),
                          CustomTextFormField(
                            enabled: false,
                            controller: orderDateController,
                            hintText: 'Enter Order Date',
                            suffix: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CustomImageView(
                                color: AppColors.primary,
                                onTap: (){
                                  _selectDate(context, (onDateSelected){
                                      orderDateController.text = onDateSelected.format(pattern: 'yyyy-MM-dd');
                                  });
                                },
                                imagePath: AppIcons.calender,
                              ),
                            ),

                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),
                          const TextFieldLabel(label: 'Required Date'),
                          CustomTextFormField(
                            controller: requiredDateController,
                            hintText: 'Enter Required Date',
                            suffix: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CustomImageView(
                                color: AppColors.primary,
                                onTap: (){
                                  _selectDate(context, (onDateSelected){
                                    requiredDate = onDateSelected;
                                    requiredDateController.text = DateFormat('dd-MM-yyyy').format(onDateSelected);
                                  });
                                },
                                imagePath: AppIcons.calender,
                              ),
                            ),

                          ),

                          if(widget.toOrgName!="Material Supplier")...[
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          // LabelTextField(
                          //   label: 'Tooth Name',
                          //   hint: 'Enter Tooth Name',
                          //   controller: toothNameController,
                          //   validator: AppValidators.validateText,
                          // ),
                            const TextFieldLabel(label: 'Tooth Name'),
                            CustomDropdown(
                              dropdownWidth:
                              MediaQuery.of(context).size.width > 600
                                  ? Get.size.width * 0.5
                                  : Get.size.width - 30,
                              hintText: 'Select Tooth ',
                              items: const [
                                "Full Mouth",
                                '11',
                                '12',
                                '13',
                                '14',
                                '15',
                                '16',
                                '17',
                                '18',
                                '21',
                                '22',
                                '23',
                                '24',
                                '25',
                                '26',
                                '27',
                                '28',
                                '31',
                                '32',
                                '33',
                                '34',
                                '35',
                                '36',
                                '37',
                                '38',
                                '41',
                                '42',
                                '43',
                                '44',
                                '45',
                                '46',
                                '47',
                                '48'
                              ],
                              // Custom tooth numbers
                              onChanged: (value) {
                                setState(() {
                                  selectedToothValue = value;
                                });
                              },
                              selectedValue: selectedToothValue,
                            ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          const TextFieldLabel(label: 'Shade'),
                          CustomDropdown(
                            hintText: 'Select',
                            items: items,
                            selectedValue: selectedShade,
                            onChanged: (value) {
                              selectedShade = value;
                            },
                          )],
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          LabelTextField(
                            isMandatory: false,
                            label: 'Remarks',
                            hint: 'Write Note...',
                            maxLines: 4,
                            controller: remarkController,
                          ),
                          const SizedBox(
                            height: AppSizes.defaultSpace,
                          ),

                          CustomElevatedButton(
                            onPressed: () async{
                              recalculateTotal();
                              Map<String, dynamic> updatedOrder = {
                                "id": orderData['id'],
                                "orderId":orderData['OrderId'],
                                "toothName": toothNameController.text,
                                "doctorName": doctorNameController.text,
                                "hospitalName": hospitalNameController.text,
                                "laboratoryName": laboratoryController.text,
                                "orderDate": orderDateController.text,
                                "requiredDate": DateFormat('yyyy-MM-dd').format(requiredDate ?? DateTime.now()),
                                "delivery_boy": getData.read('userRecord')["id"],
                                "userUUID": orderData['userUUID'],
                                "shades": selectedShade,
                                "toOrganization": deliveryOrdersController.deliveryOrderDetailsModel?.data.toOrganization ?? "",
                                "remarks": remarkController.text,
                                "serviceId": selectedChoices.map((s) => {
                                  "id":s.key,
                                  "quantity":orderData['quantity']
                                }).toList(),

                                "sub_total": subTotal,
                                "tax": tax,
                                "total_amount": totalAmount,
                              };

                              try {

                                bool isSuccess = await editController.createOrder(updatedOrder);

                                if (isSuccess) {
                                  // ✅ Update order status
                                  deliveryOrdersController.fetchOrders(status: 'processing');

                                  // ✅ Navigate to success screen only if successful
                                  Get.to(()=> const DeliveryEditOrderSuccessScreen());

                                }
                              } catch (e) {
                                Get.snackbar(
                                  "Error",
                                  "An unexpected error occurred: $e",
                                  backgroundColor: AppColors.primary3,
                                  colorText: Colors.white,
                                  duration: const Duration(seconds: 3),
                                  snackPosition: SnackPosition.TOP,
                                );
                              }
                            },


                            text: 'Update',


                            //onPressed: _saveChanges
                          )




                        ],

                      ),
                    )
                  ],
                )),
          ),
        ),
      ),
    );
  }

  Widget _buildDateField(DateTime? date, Function(DateTime) onDateSelected) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            _selectDate(context, onDateSelected);
          },
          child: Container(
            height: 56,
            padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.md, vertical: AppSizes.md),
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.border12,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                    date != null
                        ? "${date.day}-${date.month}-${date.year}"
                        : "Select date",
                    style: date != null
                        ? CustomTextStyles.b2.copyWith(
                            color: AppColors.black,
                            fontWeight: FontWeight.w500,
                          )
                        : CustomTextStyles.b4Primary2
                            .copyWith(color: AppColors.darkGrey)),
                CustomImageView(
                  imagePath: AppIcons.calender,
                  color: AppColors.primary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String getOrgLabel(String? orgName) {
    // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supply";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}
