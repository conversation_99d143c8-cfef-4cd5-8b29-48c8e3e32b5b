import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:platix/controllers/delivery_boy/delivery_create_order_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_orders_controller.dart';
import 'package:platix/controllers/delivery_boy/delivery_profile_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/web_responsive_utils.dart';
import 'package:platix/view/screens/deliveryboy/delivery_checkout_success_screen.dart';
import '../../../api/data_store.dart';
import '../../../data/models/owner/owner_doctor_model.dart';
import '../../../data/models/user_model.dart';
import '../../widgets/multi_select_dropdown/core/multi_select.dart';


class DeliveryCheckoutScreen extends StatefulWidget {
  String? orgName;
   DeliveryCheckoutScreen({super.key,this.orgName});

  @override
  State<DeliveryCheckoutScreen> createState() => _DeliveryCheckoutScreenState();
}

class _DeliveryCheckoutScreenState extends State<DeliveryCheckoutScreen> {


  DeliveryProfileController profileController = Get.put(DeliveryProfileController());
  DeliveryOrdersController ordersController = Get.put(DeliveryOrdersController());
  DeliveryCreateOrderController createController = Get.put(DeliveryCreateOrderController());

  final userRecord = UserRecord.fromJson(getData.read('userRecord'));

  String latitude = "";
  String longitude = "";
  String fullAddress = ''; // Default full address


  late GoogleMapController mapController;
  LatLng showLocation = const LatLng(17.3850, 78.4867);
  String? selectedAddress;

  late final Map<String, dynamic>? args;
  late final orderData;
  late final DoctorResult? selectedDoctor;
  late final selectedServiceDetails;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    args = Get.arguments;
    orderData = args?['order_data'];
    print("Order Date: ${orderData['orderDate']}");
    print("Required Date: ${orderData['requiredDate']}");

    selectedDoctor = args?['selected_doctor'];
    selectedAddress= selectedDoctor?.address;
    selectedServiceDetails = orderData['selectedServiceDetails'] ?? [];
  }

  @override
  Widget build(BuildContext context) {

    double calculateSubtotal() {
      double subtotal = 0.0;

      for (var serviceDetail in selectedServiceDetails) {
        double price = 0.0;
        int quantity = 1;

        if (serviceDetail is Map<String, dynamic>) {
          // Correctly extract price and quantity from the passed map
          price = double.tryParse(serviceDetail['price']?.toString() ?? '0.0') ?? 0.0;
          quantity = int.tryParse(serviceDetail['quantity']?.toString() ?? '1') ?? 1;
        } else if (serviceDetail is Choice<OrganizationService>) {
          // Extract from metadata if it's a Choice<Service> (Failsafe handling)
          price = double.tryParse(serviceDetail.metadata?.price ?? '') ?? 0.0;
          quantity = 1; // Default to 1 (since Service model has no quantity)
        }

        subtotal += price * quantity;
      }

      return subtotal;
    }

    double calculateGST() {
      double subtotal = calculateSubtotal();
      return 0.18 * subtotal;  // 18% GST
    }

    double calculateTotalAmount() {
      double subtotal = calculateSubtotal();


        double gst = calculateGST();

        // double platformFee = profileController.platformFee.toDouble();

        return subtotal + gst;

    }
    double subtotal = calculateSubtotal();


    return Scaffold(
      appBar: const CustomAppBar(title: "Checkout", backgroundColor: AppColors.primary, textColor: AppColors.white),
      body: SingleChildScrollView(
        child: Padding(
          padding: kIsWeb? WebResponsiveUtils.webPadding(context) :const EdgeInsets.all(AppSizes.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: AppSizes.spaceExtraSmall,),
              Text(
                'Order Details',
                style: CustomTextStyles.b4.copyWith(height: 20/14)
              ),
              const SizedBox(height: AppSizes.spaceSmall),
              Container(
                padding: const EdgeInsets.all(AppSizes.md),



                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.16),
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                ),
                child: Column(
                  children: [
                    _buildRow('Doctor Name', "${orderData['doctor_name']}"),
                    const SizedBox(height: AppSizes.spaceSmall),
                    _buildRow('Hospital Name', "${orderData['hospital_name']}"),
                    const SizedBox(height: AppSizes.spaceSmall),

                    if(widget.orgName=="Dental Laboratory")...[
                    _buildRow('Patient Name', "${orderData['patient_name']}"),
                    const SizedBox(height: AppSizes.spaceSmall),
                   _buildRow('Patient ID', "${orderData['patient_id']}"),
                    const SizedBox(height: AppSizes.spaceSmall)],
                    _buildRow(getOrgLabel(userRecord.organization?.organizationType?.organizationType), '${orderData['laboratory_name']}'),
                  ],
                ),
              ),
              const SizedBox(height: AppSizes.spaceBtwItems),
              const Text('Selected Services:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8.0),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                  boxShadow: [
                    BoxShadow(blurRadius: 4, color: AppColors.black.withOpacity(0.16)),
                  ],
                ),
                child: ListView.builder(

                  shrinkWrap: true,
                  itemCount: selectedServiceDetails.length,
                  itemBuilder: (context, index) {
                    final serviceDetail = selectedServiceDetails[index];

                    // Handle both Map<String, dynamic> and Choice<Service>
                    String serviceName = "";
                    double price = 0.0;
                    int quantity = 1;

                    if (serviceDetail is Map<String, dynamic>) {
                      // If it's a Map, extract values safely
                      serviceName = serviceDetail['name'].toString();
                      price = double.tryParse(serviceDetail['price']?.toString() ?? '0.0') ?? 0.0;
                      quantity = int.tryParse(serviceDetail['quantity']?.toString() ?? '1') ?? 1;
                    } else if (serviceDetail is Choice<OrganizationService>) {
                      // If it's a Choice<Service>, extract from metadata
                      serviceName = serviceDetail.metadata!.servicess!.servicename ?? '';
                      price = double.tryParse(serviceDetail.metadata!.price.toString()) ?? 0.0;
                      quantity = 1; // Default since there's no quantity in `Service`
                    }

                    return SizedBox(
                      child: Column(
                        children: [
                          const SizedBox(height: AppSizes.sm2),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(serviceName, style: CustomTextStyles.b6_3),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Text(
                                      "₹${(price * quantity).toStringAsFixed(2)}",
                                      style: CustomTextStyles.b6_3,
                                    ),
                                  ],
                                ),
                                Container(
                                  height: 32,
                                  width: 104,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                                    border: Border.all(color: AppColors.primary),
                                  ),
                                  child: Row(
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.only(left: AppSizes.md, right: AppSizes.sm2),
                                        child: InkWell(
                                          child: CustomImageView(imagePath: AppIcons.remove),
                                          onTap: () {
                                            if (quantity > 1) {
                                              setState(() {
                                                if (serviceDetail is Map<String, dynamic>) {
                                                  serviceDetail['quantity'] = (quantity - 1).toString();
                                                }
                                              });
                                            }
                                          },
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 1.5),
                                        child: Text('$quantity', style: CustomTextStyles.b6_3),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(right: AppSizes.sm, left: AppSizes.sm2),
                                        child: InkWell(
                                          child: CustomImageView(imagePath: AppIcons.add, color: AppColors.primary),
                                          onTap: () {
                                            setState(() {
                                              if (serviceDetail is Map<String, dynamic>) {
                                                serviceDetail['quantity'] = (quantity + 1).toString();
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 11),
                          if (index != selectedServiceDetails.length - 1)
                            Container(height: 1, color: AppColors.grey),
                        ],
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: AppSizes.spaceBtwItems),
              Text("Shipping address", style: CustomTextStyles.b4.copyWith(height: 20/14)),
              const SizedBox(height: AppSizes.spaceSmall),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm),
                  boxShadow: [
                    BoxShadow(blurRadius: 4, color: AppColors.black.withOpacity(0.16)),
                  ],
                ),
                child: ListTile(
                  title: Text(
                    selectedAddress ?? "Shanthi Dental Hospital, 123 Main Street, S.R Nagar, Hyderabad, 500032", // Use the address if available
                    style: CustomTextStyles.b6_3,
                  ),
                  // trailing: TextButton(
                  //   onPressed: () async {
                  //     final selectedLocation = await Navigator.push(
                  //       context,
                  //       MaterialPageRoute(
                  //         builder: (context) => FullScreenMap(
                  //           initialLocation: showLocation, // Pass current location
                  //         ),
                  //       ),
                  //     );
                  //
                  //     if (selectedLocation != null) {
                  //       setState(() {
                  //         selectedAddress = selectedLocation; // Update the address with the selected one
                  //       });
                  //     }
                  //   },
                  //   child: const Text("Change"),
                  // ),
                ),
              ),

              const SizedBox(height: AppSizes.spaceBtwItems),

              Padding(
                padding: const EdgeInsets.symmetric(vertical: AppSizes.sm),
                child: BillDetailRow(label: "Total Amount", value: "₹${subtotal.toStringAsFixed(2)}"),
              ),


              const SizedBox(height: AppSizes.spaceBtwItems),

              CustomElevatedButton(
                onPressed: () async{
                  Map<String, dynamic> orderDetails = {
                    "fromOrganization": userRecord.organization?.id,
                    "patientName": orderData['patient_name'],
                    "patientId": orderData['patient_id'],
                    "orderDate": orderData['orderDate'],
                    "requiredDate": orderData['requiredDate'],
                    "userUUID": selectedDoctor?.id,
                    "toOrganization": userRecord.organization?.id,
                    "serviceId": selectedServiceDetails.map((service) => {
                      "id": service['id'],
                      "quantity": service['quantity'] ?? 1
                    }).toList(),
                    "shades": orderData['shade'],
                    "shade": orderData['shade'],
                    "remarks": orderData['remark'],
                    "delivery_boy": getData.read('userRecord')["id"],
                    "sub_total": subtotal,
                    "tax": 0.18 * subtotal,
                    "total_amount": calculateTotalAmount().toString(),
                    "address": selectedAddress,
                    'toothMappings': orderData['toothMappings'],
                    'stl_files': orderData['stl_files'],
                    'impression_type': orderData['impression_type'],
                    'shade_type': orderData['shade_type'],
                    'age': orderData['age'],
                    'gender': orderData['gender'],
                  };
                  print( getData.read('userRecord')["id"]);

                  log("Order Details from checkout screen: $orderDetails");

                  try {
                    bool isSuccess = await createController.createOrder(orderDetails);

                    if (isSuccess) {
                      // ✅ Update order status
                      ordersController.fetchOrders(status: 'processing');

                      // ✅ Navigate to success screen only if successful
                      Get.to(()=> const DeliveryCheckoutSuccessScreen() );

                    }
                  } catch (e) {
                    Get.snackbar(
                      "Error",
                      "An unexpected error occurred: $e",
                      backgroundColor: AppColors.primary3,
                      colorText: Colors.white,
                      duration: const Duration(seconds: 3),
                      snackPosition: SnackPosition.TOP,
                    );
                  }
                },
                text: 'Confirm Order',
              ),
              const SizedBox(height: AppSizes.defaultSpace),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: 4, child: Text(label, style: CustomTextStyles.b4_1)),
        Text(': ', style: CustomTextStyles.b5),
        Expanded(flex: 6, child: Text(value, style: CustomTextStyles.b5)),
      ],
    );
  }

  String getOrgLabel(String? orgName) {
    // if (orgName == null) return "Organization Name"; // Handle null case

    switch (orgName) {
      case "Dental Laboratory":
        return "Laboratory Name";
      case "Radiology":
        return "Radiology Center Name";
      case "Material Supplier":
        return "Material Supply";
      default:
        return "Organization Name"; // Default fallback
    }
  }
}


class BillDetailRow extends StatelessWidget {
  final String label;
  final String value;


  const BillDetailRow({
    super.key,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: CustomTextStyles.b6_3.copyWith(color: AppColors.darkGrey),
          ),
          Text(
            value,
            style: CustomTextStyles.b6_1.copyWith(fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}
