import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/data/models/cart_item_model.dart';
import 'package:platix/data/models/item_model.dart';
import 'package:platix/utils/constants/colors.dart';

class CartController extends GetxController {
  static CartController get find => Get.find<CartController>();

  static void init() {
    Get.put<CartController>(CartController(), permanent: true);
  }

  var cartItems = <CartItem>[].obs;
  var isLoading = false.obs;

  void addToCart(Item item) {
    final index = cartItems.indexWhere((cartItem) => cartItem.item.name == item.name);
    if (index != -1) {
      if (cartItems[index].quantity < item.quantity) {
        cartItems[index].quantity++;
      } else {
        Get.snackbar('Info', 'You cannot add more of this item',
          backgroundColor: AppColors.primary5,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      }
    } else {
      if (item.quantity > 0) {
        cartItems.add(CartItem(item: item));
      } else {
        Get.snackbar('Info', 'This item is out of stock',
          backgroundColor: AppColors.primary5,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
      }
    }
    cartItems.refresh();
  }

  void removeFromCart(CartItem cartItem) {
    cartItems.remove(cartItem);
  }

  void clearCart() {
    cartItems.clear();
  }

  void addCartItemsFromApi(List<dynamic> cartDetails, List<Item> platixItems) {
    for (var cartDetail in cartDetails) {
      final itemId = cartDetail['item_id'];
      final quantity = cartDetail['quantity'];

      final platixItem = platixItems.firstWhereOrNull((item) => item.id == itemId);

      if (platixItem != null) {
        final existingCartItem = cartItems.firstWhereOrNull((item) => item.item.id == itemId);
        if (existingCartItem != null) {
          existingCartItem.quantity = quantity;
        } else {
          cartItems.add(CartItem(item: platixItem, quantity: quantity));
        }
      }
    }
    cartItems.refresh();
  }

  void incrementQuantity(CartItem cartItem) {
    if (cartItem.quantity < cartItem.item.quantity) {
      cartItem.quantity++;
    } else {
      Get.snackbar('Info', 'You cannot add more of this item',
        backgroundColor: AppColors.primary5,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      );
    }
    cartItems.refresh();
  }

  void decrementQuantity(CartItem cartItem) {
    if (cartItem.quantity > 1) {
      cartItem.quantity--;
      cartItems.refresh();
    } else {
      removeFromCart(cartItem);
    }
  }

  double get totalPrice => cartItems.fold(0, (sum, item) => sum + (item.item.discountedPrice * item.quantity));

  int getQuantity(Item item) {
    final cartItem = cartItems.firstWhereOrNull((cartItem) => cartItem.item.name == item.name);
    return cartItem?.quantity ?? 0;
  }
}
