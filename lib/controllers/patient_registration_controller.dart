import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/patient_delete_response_model.dart';
import 'package:platix/utils/constants/colors.dart';

class PatientRegistrationController extends GetxController {
  final ApiService _apiService = ApiService();
  
  // Observable variables
  var isLoading = false.obs;
  var patientRegistrations = <PatientData>[].obs;
  var paginationData = PaginationData(total: 0, page: 1, limit: 10, totalPages: 0).obs;
  var searchQuery = ''.obs;
  var currentPage = 1.obs;
  var itemsPerPage = 10.obs;
  var hasError = false.obs;
  var errorMessage = ''.obs;

  // Controllers
  final TextEditingController searchController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    super.onInit();
    fetchPatientRegistrations();
    _setupScrollListener();
  }

  @override
  void onClose() {
    searchController.dispose();
    scrollController.dispose();
    super.onClose();
  }

  void _setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.pixels == scrollController.position.maxScrollExtent) {
        if (paginationData.value.hasNextPage && !isLoading.value) {
          loadNextPage();
        }
      }
    });
  }

  Future<void> fetchPatientRegistrations({bool isRefresh = false}) async {
    try {
      if (isRefresh) {
        currentPage.value = 1;
        patientRegistrations.clear();
      }
      
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      final response = await _apiService.getAllRegistrations(
        page: currentPage.value,
        limit: itemsPerPage.value,
        search: searchQuery.value,
      );

      if (isRefresh) {
        patientRegistrations.value = response.data;
      } else {
        patientRegistrations.addAll(response.data);
      }
      
      paginationData.value = response.pagination;
      
    } catch (e) {
      hasError.value = true;
      errorMessage.value = e.toString().replaceAll('Exception: ', '');
      print('Error fetching patient registrations: $e');
      
      // Show error snackbar
      Get.snackbar(
        'Error',
        'Failed to load patient registrations: ${errorMessage.value}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadNextPage() async {
    if (paginationData.value.hasNextPage && !isLoading.value) {
      currentPage.value = paginationData.value.nextPage;
      await fetchPatientRegistrations();
    }
  }

  Future<void> refreshData() async {
    await fetchPatientRegistrations(isRefresh: true);
  }

  void onSearchChanged(String query) {
    searchQuery.value = query;
    currentPage.value = 1;
    fetchPatientRegistrations(isRefresh: true);
  }

  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
    currentPage.value = 1;
    fetchPatientRegistrations(isRefresh: true);
  }

  void changeItemsPerPage(int newLimit) {
    itemsPerPage.value = newLimit;
    currentPage.value = 1;
    fetchPatientRegistrations(isRefresh: true);
  }

  // Helper methods for UI
  String get totalRecordsText => 'Total: ${paginationData.value.total} patients';
  
  String get paginationText => 
      'Page ${paginationData.value.page} of ${paginationData.value.totalPages}';
  
  bool get hasData => patientRegistrations.isNotEmpty;
  
  bool get showLoadMore => 
      paginationData.value.hasNextPage && !isLoading.value && hasData;

  // Get patient by ID (if needed)
  PatientData? getPatientById(String patientId) {
    try {
      return patientRegistrations.firstWhere(
        (patient) => patient.patientRegId == patientId,
      );
    } catch (e) {
      return null;
    }
  }

  // Filter patients locally (if needed for additional filtering)
  List<PatientData> getFilteredPatients({String? filterBy}) {
    if (filterBy == null || filterBy.isEmpty) {
      return patientRegistrations.toList();
    }
    
    return patientRegistrations.where((patient) {
      return patient.displayName.toLowerCase().contains(filterBy.toLowerCase()) ||
             patient.email?.toLowerCase().contains(filterBy.toLowerCase()) == true ||
             patient.mobile?.contains(filterBy) == true ||
             patient.patientRegId?.toLowerCase().contains(filterBy.toLowerCase()) == true;
    }).toList();
  }

  // Delete patient method
  Future<void> deletePatient(String patientId, String patientName) async {
    try {
      isLoading.value = true;
      
      final response = await _apiService.deletePatient(patientId);
      
      if (response.isSuccess) {
        // Remove patient from local list using database ID
        patientRegistrations.removeWhere((patient) => patient.id == patientId);
        
        // Update pagination data
        paginationData.value = PaginationData(
          total: paginationData.value.total - 1,
          page: paginationData.value.page,
          limit: paginationData.value.limit,
          totalPages: paginationData.value.totalPages,
        );
        
        // Show success message
        Get.snackbar(
          'Success',
          response.message,
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary4,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
        
        // Refresh data to ensure consistency
        await refreshData();
      } else {
        // Show error message
        Get.snackbar(
          'Error',
          response.error ?? 'Failed to delete patient',
          snackPosition: SnackPosition.TOP,
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
        );
      }
    } catch (e) {
      print('Error deleting patient: $e');
      Get.snackbar(
        'Error',
        'Failed to delete patient: ${e.toString().replaceAll('Exception: ', '')}',
        snackPosition: SnackPosition.TOP,
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }
}