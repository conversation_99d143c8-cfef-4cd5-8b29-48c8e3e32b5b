import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/data_store.dart';

import '../api/config.dart';
import '../data/models/user_model.dart';
import '../main.dart';
import '../services/api_service.dart';
import '../view/screens/email_verification.dart';
import '../view/screens/role_selection_screen.dart';
import 'package:http/http.dart' as http;
import '../data/models/role_list_model.dart';

class RoleSelectionController extends GetxController {
  var isLoading = false;
  var roles = <RoleModel>[].obs;
  String apiUrl = '${Config.baseUrl}admin/viewrole';
  var selectedOption = Rx<String?>(null); // Reactive value to manage selection
  var selectedRoleId = Rx<String?>(null); // Reactive value to manage selection
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();


  var selectedDentist = Rx<String?>(null);
  var selectedRadiology = Rx<String?>(null);
  var selectedLaboratory = Rx<String?>(null);
  var selectedMaterialSupply = Rx<String?>(null);

  TextEditingController pinController = TextEditingController();
  // Stores fetched roles from API
  // Loading state
  UserModel? userModel;


  final TextEditingController firstName = TextEditingController();
  final TextEditingController lastName = TextEditingController();
  final TextEditingController email = TextEditingController();

  //final SignInController signInController = Get.find<SignInController>();

  @override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    fetchRoles();
  }

  /// ✅ Fetch roles from API
  Future<void> fetchRoles() async {
    try {
      isLoading = true; // ✅ Set loading state
      final response = await http.get(Uri.parse(apiUrl));

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        final List<
            RoleModel> fetchedRoles = (jsonData['formattedRoles'] as List)
            .map((role) => RoleModel.fromJson(role))
            .toList();
        roles.assignAll(fetchedRoles);
      } else {
        Get.snackbar("Error", "Failed to load roles",
            snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      }
    } catch (e) {
      // Get.snackbar("Error", "Something went wrong: $e",
      //     snackPosition: SnackPosition.TOP);
    } finally {
      isLoading = false; // ✅ Stop loading
    }
  }

  /// Methods for selecting roles
  void selectDentist(String? value){
    resetSelection();
    selectedDentist.value = value;
    updateSelectedRoleId(value!);
    update();
  }

  void selectRadiology(String? value){
    resetSelection();
    selectedRadiology.value = value;
    updateSelectedRoleId(value!);
    update();
  }

  void selectLaboratory(String? value){
    resetSelection();
    selectedLaboratory.value = value;
    updateSelectedRoleId(value!);
    update();
  }

  void selectMaterialSupply(String? value){
    resetSelection();
    selectedMaterialSupply.value = value;
    updateSelectedRoleId(value!);
    update();
  }

  /// Method to update roleId
  void updateSelectedRoleId(String roleName){

    RoleModel? selectedRole = roles.firstWhereOrNull((role) => role.rolename == roleName);

    if (selectedRole == null) {
      log("❌ Role not found for name: $roleName");
    } else {
      log("✅ Role ID Found: ${selectedRole.id} for Role: $roleName");
    }

    selectedRoleId.value = selectedRole?.id;
    update();
  }

  /// To clear previous selection
  void resetSelection() {
    selectedDentist.value = null;// Clear previous selection
    selectedLaboratory.value =null;// Clear previous selection
    selectedRadiology.value = null;// Clear previous selection
    selectedMaterialSupply.value = null;// Clear previous selection
    update();
  }

  /// Validation method for form fields
  String? validateField(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// For form submission


  // Future<void> submitForm() async {
  //   isLoading = true;
  //   update();
  //
  //   final userRecord = getData.read("userRecord");
  //   final String? token = getData.read("token");
  //
  //   log("🔹 Retrieved Token: $token");
  //
  //   String firstNameTrimmed = firstName.text.trim();
  //   String lastNameTrimmed = lastName.text.trim();
  //   String emailTrimmed = email.text.trim();
  //
  //   if (selectedRoleId.value == null || selectedRoleId.value!.isEmpty) {
  //     log("❌ Validation Error: Role ID is missing.");
  //     Get.snackbar("Error", "Invalid role selection. Please try again.",
  //         snackPosition: SnackPosition.TOP);
  //     isLoading = false;
  //     update();
  //     return;
  //   }
  //
  //   log("🔹 Assigned Role ID: ${selectedRoleId.value}");
  //
  //   if (userRecord == null) {
  //     log("❌ No user record found in storage");
  //     Get.snackbar("Error", "User record not found.", snackPosition: SnackPosition.TOP);
  //     isLoading = false;
  //     update();
  //     return;
  //   }
  //
  //   final String userId = userRecord["id"];
  //   log("🔹 Retrieved User ID: $userId");
  //
  //   // ✅ Store role ID persistently
  //   final prefs = await SharedPreferences.getInstance();
  //   await prefs.setString("userRoleId", selectedRoleId.value!);
  //
  //   log("🔹 Stored Role ID in SharedPreferences: ${prefs.getString('userRoleId')}");
  //
  //   var data = {
  //     "firstName": firstNameTrimmed,
  //     "lastName": lastNameTrimmed,
  //     "email": emailTrimmed,
  //     "role_id": selectedRoleId.value,
  //     "id": userId
  //   };
  //
  //   try {
  //     log("🔄 Sending API Request...");
  //     log("📤 Request Body: ${jsonEncode(data)}");
  //
  //     var response = await ApiService.postRequest(Config.baseUrl + Config.createRoleApi,
  //         data,
  //         headers: {
  //           "Authorization": "Bearer $token",
  //           "Content-Type": "application/json"
  //         }
  //     );
  //
  //     if (response != null && response.containsKey("message")) {
  //       log("✅ API Success: $response");
  //       Get.snackbar("Success", response["message"],
  //           snackPosition: SnackPosition.TOP);
  //
  //       // ✅ Navigate to OTP Verification Screen
  //       Get.to(() => const Emailsuccessverification());
  //     } else {
  //       log("⚠️ Unexpected Response Format: $response");
  //       Get.snackbar("Error", "Unexpected response format.",
  //           snackPosition: SnackPosition.TOP);
  //     }
  //   } catch (e) {
  //     log("❌ Unexpected Exception: $e");
  //     Get.snackbar("Error", "Something went wrong. Please try again.",
  //         snackPosition: SnackPosition.TOP);
  //   } finally {
  //     isLoading = false;
  //     update();
  //   }
  // }

  Future<bool> submitForm() async {
    isLoading = true;
    update();

    final userRecord = getData.read("userRecord");
    final String? token = getData.read("token");

    log("🔹 Retrieved Token: $token");

    /// Trim spaces to avoid validation issues
    String firstNameTrimmed = firstName.text.trim();
    String lastNameTrimmed = lastName.text.trim();
    String emailTrimmed = email.text.trim();

    /// Ensure role ID is correctly assigned
    if (selectedRoleId.value == null || selectedRoleId.value!.isEmpty) {
      log("❌ Validation Error: Role ID is missing.");
      Get.snackbar("Error", "Invalid role selection. Please try again.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      isLoading = false;
      update();
      return false;
    }

    log("🔹 Assigned Role ID: ${selectedRoleId.value}");

    if (userRecord == null) {
      log("❌ No user record found in storage");
      Get.snackbar("Error", "User record not found.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      isLoading = false;
      update();
      return false; // Stop execution
    }

    final String userId = userRecord["id"];
    log("🔹 Retrieved User ID: $userId");

    // ✅ Store role ID in GetStorage instead of SharedPreferences
    getData.write("userRoleId", selectedRoleId.value!);

    log("✅ Stored Role ID in GetStorage: ${getData.read('userRoleId')}");

    var data = {
      "firstName": firstNameTrimmed,
      "lastName": lastNameTrimmed,
      "email": emailTrimmed,
      "role_id": selectedRoleId.value,
      "id": userId
    };

    try {
      log("🔄 Sending API Request...");
      log("📤 Request Body: ${jsonEncode(data)}");

      var response = await ApiService.postRequest(
          Config.baseUrl + Config.createRoleApi,
          data,
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json"
          }
      );

      if (response != null && response.containsKey("message")) {
        log("✅ API Success::::: $response");
        Get.snackbar("Success", response["message"], snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary4,colorText: Colors.white,duration: const Duration(seconds: 3));
        getData.write("userRecord", response["user"]);
        await requestEmailOtp(isFromOTPScreen: true);
        // ✅ Navigate to OTP Verification Screen
        Get.to(() => const EmailSuccessVerification());
      } else {
        log("⚠️ Unexpected Response Format: $response");
        Get.snackbar("Error", "Unexpected response format.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      }
    } on TimeoutException {
      log("⏳ Timeout: Server took too long to respond");
      Get.snackbar("Error", "Request timed out. Please try again.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
    } on SocketException {
      log("📶 No Internet Connection");
      Get.snackbar("Error", "No internet connection. Check your network.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
    } on FormatException {
      log("❌ Invalid API Response Format");
      Get.snackbar("Error", "Server response was not as expected.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
    } on UserNotFoundException catch (e) {
      return true;
    }catch (e) {
      log("❌ Unexpected Exception: $e");
      Get.snackbar("Error", "All fields are required. Please fill.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
    } finally {
      isLoading = false;
      update();
    }
    return false;
  }

  Future<bool> requestEmailOtp({bool isFromOTPScreen = false}) async {
    isLoading = true;
    update();

    final userRecord = getData.read("userRecord");
    final String? token = getData.read('token');

    log("🔹 Retrieved Token: $token");

    if (userRecord == null) {
      log("❌ No user record found in storage");
      Get.snackbar("Error", "User record not found.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      isLoading = false;
      update();
      return false;
    }
    var data = {
      "email": userRecord['email'],
    };

    try {
      log("🔄 Sending API Request...");
      var val = await ApiService.postRequest(Config.baseUrl + Config.requestEmailOtp,
          data,
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json"
          }
      );

      log("📥 Full API Response: $val");

      if (val != null) {
        if(!isFromOTPScreen){
          Get.to(() => const EmailSuccessVerification(isFromHome: true,));
        }
       return true;
      } else {
        log("⚠️ Unexpected Response Format: $val");
        Get.snackbar("Error", "Failed to send Email", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
        return false;
      }

    } catch (e) {
      log("❌ Exception: $e");
      Get.snackbar("Error", "Failed to send Email", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> verifyEmailOtp2() async {
    isLoading = true;
    update();

    final userRecord = getData.read("userRecord");
    final String? token = getData.read('token');

    log("🔹 Retrieved Token: $token");

    if (userRecord == null) {
      log("❌ No user record found in storage");
      Get.snackbar("Error", "User record not found.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      isLoading = false;
      update();
      return;
    }

    final String userId = userRecord["id"];
    log("🔹 Retrieved User ID: $userId");

    // ✅ Retrieve role ID from SharedPreferences
    //final prefs = await SharedPreferences.getInstance();
    // final storedRoleId = prefs.getString('userRoleId');
    final storedRoleId =getData.read("userRoleId");


    if (storedRoleId != null) {
      log("🔹 Retrieved Role ID from SharedPreferences in verifyEmailOtp: $storedRoleId");
      selectedRoleId.value = storedRoleId; // Assign to variable
    } else {
      log("🟡 No role_id found in SharedPreferences");
    }

    var data = {
      "id": userId,
      "otp": pinController.text
    };

    try {
      log("🔄 Sending API Request...");
      var val = await ApiService.postRequest(Config.baseUrl + Config.verifyEmailOtp,
          data,
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json"
          }
      );

      log("📥 Full API Response: $val");

      if (val != null && val.containsKey("message")) {
        log("✅ API Success: $val");
        Get.snackbar("Success", val["message"], snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary4,colorText: Colors.white,duration: const Duration(seconds: 3));
          await handleLogin(val);
          checkRoleAndNavigate(val['user']['role_id']);
      } else {
        log("⚠️ Unexpected Response Format: $val");
        Get.snackbar("Error", "Unexpected response format.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      }

    } catch (e) {
      log("❌ Exception: $e");
      //Get.snackbar("Error", "Incorrect OTP, Please enter valid OTP", snackPosition: SnackPosition.TOP);
    } finally {
      isLoading = false;
      update();
    }
  }



  Future<void> verifyEmailOtp() async {
    isLoading = true;
    update();

    final userRecord = getData.read("userRecord");
    final String? token = getData.read('token');

    log("🔹 Retrieved Token: $token");

    if (userRecord == null) {
      log("❌ No user record found in storage");
      Get.snackbar("Error", "User record not found.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      isLoading = false;
      update();
      return;
    }

    final String userId = userRecord["id"];
    log("🔹 Retrieved User ID: $userId");

    // ✅ Retrieve role ID from SharedPreferences
    //final prefs = await SharedPreferences.getInstance();
   // final storedRoleId = prefs.getString('userRoleId');
    final storedRoleId =getData.read("userRoleId");


    if (storedRoleId != null) {
      log("🔹 Retrieved Role ID from SharedPreferences in verifyEmailOtp: $storedRoleId");
      selectedRoleId.value = storedRoleId; // Assign to variable
    } else {
      log("🟡 No role_id found in SharedPreferences");
    }

    var data = {
      "id": userId,
      "otp": pinController.text
    };

    try {
      log("🔄 Sending API Request...");
      var val = await ApiService.postRequest(Config.baseUrl + Config.verifyEmailOtp,
          data,
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json"
          }
      );

      log("📥 Full API Response: $val");

      if (val != null && val.containsKey("message")) {
        log("✅ API Success: $val");
        Get.snackbar("Success", val["message"], snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary4,colorText: Colors.white,duration: const Duration(seconds: 3));

        // ✅ Check if role exists in Role List API
        if (selectedRoleId.value != null && selectedRoleId.value!.isNotEmpty) {
          log("🔹 Navigating based on role: ${selectedRoleId.value}");

          await handleLogin(val);
          log("checking whether AI callling or not");
          checkRoleAndNavigate(selectedRoleId.value!);
        } else {
          log("🟡 No role_id found, navigating to Role Selection...");
          Get.offAll(() => const RoleSelection());
        }
      } else {
        log("⚠️ Unexpected Response Format: $val");
        Get.snackbar("Error", "Unexpected response format.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      }

    } catch (e) {
      log("❌ Exception: $e");

      //Get.snackbar("Error", "Incorrect OTP, Please enter valid OTP", snackPosition: SnackPosition.TOP);
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> handleLogin(var val) async {
    if (val != null && val['message'] != null) {
      log("✅ API Success: ${val['message']}");

      // Extract user data from the response
      if (val.containsKey('user')) {
        final userRecord = val['user'];  // Extract the 'user' data
        log("✅ User data retrieved: $userRecord");

        // Create a UserModel from the response
        this.userModel = UserModel(
          message: val['message'],
          userRecord: UserRecord.fromJson(userRecord), // Construct userRecord
          token: '',  // Since there's no token in the response, leave it empty or null
        );
        log("✅ UserModel assigned!");

        // Save user details persistently using GetStorage
        getData.write("userRecord", userModel?.userRecord?.toJson());

        log("🔹 User details saved: ${jsonEncode(getData.read("userRecord"))}");

        // // Check if role_id exists and navigate accordingly
        // final String? userRoleId = userModel?.userRecord.roleId;
        // if (userRoleId != null) {
        //   log("🔹 Retrieved Role ID: $userRoleId");
        //   checkRoleAndNavigate(userRoleId);
        // } else {
        //   log("🟡 No role_id found, navigating to Role Selection...");
        //   Get.offAll(() => const RoleSelection());
        // }

       // AppUtils.showToastMessage("OTP verification successful!", toastType: ToastType.success);
      } else {
        log("❌ Missing user data in response.");
        Get.snackbar("Error", "User data missing in response.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
      }
    } else {
      log("❌ API returned null or unexpected response.");
      Get.snackbar("Error", "Unexpected response format or missing message.", snackPosition: SnackPosition.TOP,backgroundColor: AppColors.primary3,colorText: Colors.white,duration: const Duration(seconds: 3));
    }
  }



  Future<void> checkRoleAndNavigate(String userRoleId) async {
    log("🔄 Fetching role list to validate role_id...");

    try {
      var response = await ApiService.getRequest(Config.baseUrl + Config.roleListApi);

      if (response != null && response.containsKey("formattedRoles")) {
        List roles = response["formattedRoles"];
        print(roles);
        bool roleExists = roles.any((role) => role["id"] == userRoleId);
          print('Role_id : $userRoleId');
        if (roleExists) {
          log("✅ Role ID matched, navigating to home...");
          Get.offAll(() => getHomeScreen(userRoleId));  // ✅ Navigate to correct screen
        } else {
          log("🟡 Role ID not found in role list, redirecting to Role Selection...");
          // Get.offAll(() => const RoleSelection());
        }
      } else {
        log("❌ Failed to fetch role list.");
        // Get.offAll(() => const RoleSelection());
      }
    } catch (e) {
      log("❌ Exception while fetching roles: $e");
      // Get.offAll(() => const RoleSelection());
    }
  }


}
