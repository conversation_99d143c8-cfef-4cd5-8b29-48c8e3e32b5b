import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/utils/constants/colors.dart';
import 'dart:convert';

import '../../api/config.dart';
import '../../api/data_store.dart';

class DentistCreateOrderController extends GetxController {
  var isLoading = false.obs;
  bool cashFreeLoading = false;

  HomeController homeController=Get.find();

  Future<bool> createOrder(Map<String, dynamic> orderData) async {
    String token = getData.read("token") ?? '';
    log("Creating order with data: $orderData");

    try {
      isLoading(true);
      const String apiUrl = "${Config.baseUrl}dentist/upsert";

      var request = http.MultipartRequest('POST', Uri.parse(apiUrl));
      request.headers['Authorization'] = 'Bearer $token';

      // Create a copy of the map and remove the file before encoding
      final Map<String, dynamic> orderDataForJson = Map.from(orderData);
      orderDataForJson.remove('stl_files');

      // Add each field of the json as a field in the multipart request
      orderDataForJson.forEach((key, value) {
        if (value is List) {
          for (int i = 0; i < value.length; i++) {
            if (value[i] is Map) {
              (value[i] as Map).forEach((k, v) {
                request.fields['$key[$i][$k]'] = v.toString();
              });
            }
          }
        } else {
          request.fields[key] = value.toString();
        }
      });

      // Add the STL file if it exists
     if (orderData['stl_files'] != null) {
        log("Adding STL files: ${orderData['stl_files']}");
        for (var file in orderData['stl_files']) {
          request.files.add(await http.MultipartFile.fromPath(
            'files', // The field name expected by the server for an array of files
            file.path,
          ));

        }
      }
      log("requestgfgfh: $request.files");
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();
      log('Response Status: $response');
      log('Response Body: $responseBody');
      log('Request URL: $apiUrl');
      log('Order Data: $orderData');

      if (response.statusCode == 200 || response.statusCode == 201) {
        homeController.getDashboard();
        return true;
      } else {
        Get.snackbar("Error", "Failed to create order",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        log("Error: ${response.statusCode} - $responseBody");
        return false;
      }
    } catch (e) {
      log("Exception gtyuyu: $e");
      Get.snackbar("Error", "Something went wrong",
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      );
      return false;
    } finally {
      isLoading(false);
    }
  }

  Future<Map<String, String>?> createCashFreeOrder(Map<String, dynamic> request) async {

    String token = getData.read("token") ?? '';
    try {
     const String apiUrl = Config.baseUrl + Config.createCashFreeOrder;

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {
          'orderId': data["order_id"],
          'sessionId': data["payment_session_id"],
        };
      } else {
        Get.snackbar("Error", "Failed to Generate Order",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        print("Error: ${response.body}");
      }
    } catch (e) {
      Get.snackbar("Error", "Something went wrong",
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      );
      print("Exception: $e");
    } finally {

    }
    return null;
  }


  Future<void> checkStatus(String orderId) async {
    String token = getData.read("token") ?? '';
    try {
      isLoading(true);
      String apiUrl = Config.baseUrl+Config.checkPaymentStatus(orderId);

      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final cartController = Get.find<CartController>();
        cartController.clearCart();
      } else {
        Get.snackbar("Error", "Failed to get Order Status",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        print("Error: ${response.body}");
      }
    } catch (e) {
      // Get.snackbar("Error", "Something went wrong");
      print("Exception: $e");
    } finally {
      isLoading(false);
    }
  }

}
