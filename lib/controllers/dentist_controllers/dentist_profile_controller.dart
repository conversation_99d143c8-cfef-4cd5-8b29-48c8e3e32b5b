import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;

import 'package:mime/mime.dart';
import 'package:platix/data/models/dentist/dentist_organizations.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/signin_screen.dart';
import '../../api/config.dart';
import '../../api/data_store.dart';
import '../../data/models/dentist/dentist_t&c_privacy_model.dart';
import '../../utils/app_utils.dart';
import '../../utils/enums.dart';

class ProfileController extends GetxController {
  var isLoading = false.obs;
  var isDentistsOrganizationsLoading = false.obs;
  var selectedOption = "".obs;
  var userData = {}.obs;
  List<Datum>? dentistOrganizations;

  var selectedPrefix = "".obs;
  var selectedType = "".obs;

  String platformFee = "0";
  var cleanCurrentSelection = false.obs;


  Future<List<Datum>> searchHospitals(String hospital) async {
    try {
      final query = hospital.toLowerCase().trim();

      print("📦 dentistOrganizations.length: ${dentistOrganizations?.length}");

      List<Datum> allData = dentistOrganizations ?? []; // No need for expand

      for (var datum in allData) {
        print("🏥 Datum name: '${datum.name}', address: '${datum.address}'");

      }

      List<Datum> filtered = allData.where((datum) {
        final nameMatch = datum.name.toLowerCase().contains(query);
        final addressMatch = datum.address.isNotEmpty ? datum.address.first.toLowerCase().contains(query)  : false;
        final userHospitalMatch = datum.name.toLowerCase().contains(query);

        return nameMatch || addressMatch || userHospitalMatch;
      }).toList();

      print("✅ Filtered results: ${filtered.length}");
      return filtered;
    } catch (e) {
      log('❌ Exception in searchHospitals: $e');
      return [];
    }
  }




  // Fetch dentist organizations data
  Future<void> getDentistOrganizations() async {
    isDentistsOrganizationsLoading.value = true;
    update(); // Notify UI to rebuild (optional if you're using `GetX` reactivity)

    if (dentistOrganizations != null) {
      isDentistsOrganizationsLoading.value = false;
      update();
      return;
    }

    const String url = Config.baseUrl + Config.getDentistOrganizations;

    try {
      // Retrieve token from GetStorage (or wherever you're storing it)
      String token = getData.read("token") ?? '';  // Replace with your storage method

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Log the data structure to verify it
        log("✅ Parsed Response: $data");

        if (data['data'] != null && data['data'] is List) {
          final dentistOrganizationsData = List<Datum>.from(
              data['data'].map((orgData) => Datum.fromJson(orgData)));

          dentistOrganizations = dentistOrganizationsData;
          //log("✅ dentistOrganizations Fetched: $dentistOrganizationsData");
        } else {
          log("⚠️ Unexpected data format or 'data' is null: ${data['data']}");
        }
      } else {
        log("⚠️ Error: ${response.statusCode} - ${response.body}");
        return;
      }
    } catch (e) {
      log("❌ Exception: $e");
      return;
    }

    isDentistsOrganizationsLoading.value = false;
    update(); // Notify UI that loading is finished
  }

  Future<void> fetchUserProfile() async {
    try {

      print("🔵 Checking stored user data: ${getData.read("userRecord")}");

      isLoading(true);
      String? storedUser = getData.read("userRecord");
      if (storedUser != null) {
        userData.value = jsonDecode(storedUser);
      }
    } catch (e) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
       // Get.snackbar("Error", "Failed to fetch user profile");
      });
    } finally {
      isLoading(false);
    }
  }


  Future<bool> updateUserProfile(Map<String, dynamic> updatedData, XFile? pickedFile) async {
    try {
      isLoading(true);
      update();

      String? userId = getData.read("userRecord")?["id"];
      String? token = getData.read("token");

      if (userId == null || userId.isEmpty || token == null || token.isEmpty) {
        Get.snackbar("Error", "Missing User ID or Token",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        return false;
      }

      var uri = Uri.parse("${Config.baseUrl}profile/edit");
      var request = http.MultipartRequest("PUT", uri);
      request.fields["id"] = userId;

      updatedData.forEach((key, value) {
        if (value != null && value.toString().isNotEmpty) {
          request.fields[key] = value.toString();
        }
      });

      // ✅ Handle Image Upload Properly
      if (pickedFile != null) {
        if (kIsWeb) {
          Uint8List imageBytes = await pickedFile.readAsBytes();
          String? mimeType = lookupMimeType(pickedFile.path) ?? "image/jpeg";

          var multipartFile = http.MultipartFile.fromBytes(
            'profileImage',
            imageBytes,
            filename: 'profile.${mimeType.split('/').last}',
            contentType: MediaType.parse(mimeType),
          );
          request.files.add(multipartFile);
        } else {
          File file = File(pickedFile.path);
          if (await file.exists()) {
            // ✅ Resize image before upload (optional, reduces large file errors)
            final img.Image? image = img.decodeImage(await file.readAsBytes());
            if (image != null) {
              final img.Image resizedImage = img.copyResize(image, width: 500);
              final File resizedFile = File(file.path)
                ..writeAsBytesSync(img.encodeJpg(resizedImage, quality: 80));

              // ✅ Get Correct MIME Type
              String? mimeType = lookupMimeType(resizedFile.path) ?? "image/jpeg";

              request.files.add(await http.MultipartFile.fromPath(
                "profileImage",
                resizedFile.path,
                contentType: MediaType.parse(mimeType),
              ));
            } else {
              // If image decoding fails, upload as-is
              request.files.add(await http.MultipartFile.fromPath(
                "profileImage",
                file.path,
              ));
            }
          } else {
            print("❌ File does not exist.");
          }
        }
      }

      request.headers.addAll({
        "Authorization": "Bearer $token",
      });

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      print("🔴 API Response Status Code: ${response.statusCode}");
      print("🔴 API Response Headers: ${response.headers}");
      print("🔴 API Response Body: $responseData");

      if (response.statusCode == 200) {
        var responseJson = jsonDecode(responseData);
        if (responseJson["data"] != null) {
          print("ddddddddddddddddddddddddd");
          getData.write("userRecord", responseJson["data"]);
          userData.value = responseJson["data"];
          update();
          AppUtils.showToastMessage('Profile updated successfully', toastType: ToastType.success);
          return true;
        } else {
          print("❌ API returned null user data!");
          return false;
        }
      } else {
        Get.snackbar("Error", "Server error",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );

        return false;
      }
    } catch (e) {
      print("❌ Exception: $e");
      //.snackbar("Error", "Something went wrong: $e");
      return false;
    } finally {
      isLoading(false);
      update();
    }
  }


  Future<void> deleteAccount() async {
    String? email = getData.read("userRecord")?["email"];
    String token = getData.read("token");
    print(token);

    if (email == null || email.isEmpty) {
      print("❌ No email found in local storage");
      return;
    }

    try {
      var response = await http.delete(
        Uri.parse(Config.baseUrl + Config.deleteApi),
        headers: {"Content-Type": "application/json","Authorization":"Bearer $token"},
        body: jsonEncode({"email": email}),
      );

      if (response.statusCode == 200) {
        print("✅ Account deleted successfully");

        // Clear local storage and navigate to login screen
        getData.erase();
        // Get.off(const SigninScreen());
        Get.offAll(() => const SigninScreen());
      } else {
        print("❌ Failed to delete account: ${response.body}");
      }
    } catch (e) {
      print("❌ Exception: $e");
    }
  }
  // Function to change dropdown selection
  void changeSelectedOption(String value) {
    selectedOption.value = value;
  }


  Future<Settings?> fetchSettings() async {
    const String url = "${Config.baseUrl}settings";

    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final settings = settingsFromJson(response.body);
        log("✅ Settings Fetched: ${settings.toJson()}");

        // ✅ Store the platform fee
        platformFee = settings.settings.platformFee;
        log("💰 Platform Fee Stored: $platformFee");

        return settings;
      } else {
        log("⚠️ Error: ${response.statusCode} - ${response.body}");
        return null;
      }
    } catch (e) {
      log("❌ Exception: $e");
      return null;
    }
  }

}
