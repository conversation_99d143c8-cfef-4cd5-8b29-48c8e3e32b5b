

import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';

import "./../../api/data_store.dart";
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'package:platix/api/config.dart';
import 'package:platix/data/models/owner/owner_doctor_model.dart';
import 'package:platix/data/models/owner/owner_hospitals_model.dart';
import 'package:platix/services/api_service.dart';
import 'package:platix/utils/app_export.dart';
import '../../../data/models/user_model.dart' as user_model;

import '../../data/models/owner/owner_report_details_info.dart';
import '../../view/screens/owner/owner_createorder_screen.dart';
import '../../view/widgets/multi_select_dropdown/core/multi_select.dart';

class OwnerCreateOrderController extends GetxController implements GetxService{
  bool isLoading = false;
  // RxList<Choice<user_model.OrganizationService>> selectedServices = <Choice<user_model.OrganizationService>>[].obs;
  OwnerDoctorModel? ownerDoctorModel;
  RxBool showNoResultsMessage = false.obs;
  OwnerHospitalModel? ownerHospitalModel;
  List<HospitalName> hospitalList = [];
  HospitalName? selectedHospital;
  RxBool cleanCurrentSelection = false.obs;
  RxBool cleanCurrentSelection1 = false.obs;

  // void initializeServices(List<OwnerReportService> services){
  //  selectedServices.value = services.map((orgService) => Choice(orgService.orgservice?.id, orgService.orgservice?.services?.servicename ?? '', metadata: user_model.OrganizationService(id: orgService.orgservice?.id,servicess: user_model.Services(id: orgService.orgservice?.id, servicename: orgService.orgservice?.services?.servicename, servicedescription: orgService.orgservice?.services?.servicedescription),price: orgService.orgservice?.price))).toList();
  //
  //  update();
  // }

  Future<bool> createOrder(Map<String, dynamic> orderData) async {
    String token = getData.read("token") ?? '';
    log("Creating order with data: $orderData");
    isLoading = true;
    update();

    try {
      const String apiUrl = "${Config.baseUrl}owner/upsert";
      // var response = await ApiService.postRequest(Config.baseUrl + Config.ownerCreateOrder, orderData);
      var request = http.MultipartRequest('POST', Uri.parse(apiUrl));
      request.headers['Authorization'] = 'Bearer $token';

      // Create a copy of the map and remove the file before encoding
      final Map<String, dynamic> orderDataForJson = Map.from(orderData);
      orderDataForJson.remove('stl_files');

      // Add each field of the json as a field in the multipart request
      orderDataForJson.forEach((key, value) {
        if (value is List) {
          for (int i = 0; i < value.length; i++) {
            if (value[i] is Map) {
              (value[i] as Map).forEach((k, v) {
                request.fields['$key[$i][$k]'] = v.toString();
              });
            }
          }
        } else {
          request.fields[key] = value.toString();
        }
      });

      // Add the STL file if it exists
     if (orderData['stl_files'] != null) {
        log("Adding STL files: ${orderData['stl_files']}");
        for (var file in orderData['stl_files']) {
          request.files.add(await http.MultipartFile.fromPath(
            'files', // The field name expected by the server for an array of files
            file.path,
          ));

        }
      }


      // if (response != null && response['success'] == true) {
      //   return true;
      // } else {
      //   AppUtils.showToastMessage('Failed To Create Order');
      //   return false;
      // }

      log("requestgfgfh: $request.files");
      final response = await request.send();
      final responseBody = await response.stream.bytesToString();
      log('Response Status: $response');
      log('Response Body: $responseBody');
      log('Request URL: $apiUrl');
      log('Order Data: $orderData');

       if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        Get.snackbar("Error", "Failed to create order",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        log("Error: ${response.statusCode} - $responseBody");
        return false;
      }
    } catch (e) {
      log('❌ Exception: $e');
      // AppUtils.showToastMessage('Something went wrong!');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<bool> createDoctor(Map<String, dynamic> orderData) async {
    isLoading = true;
    update();

    try {
      var response = await ApiService.postRequest(Config.baseUrl + Config.ownerCreateDoctor, orderData);
      log("📥 Request body : $orderData");
      log('📥 Api Response : ${jsonEncode(response)}');

      if (response != null) {
        Get.back();
        AppUtils.showToastMessage('Doctor Added Successfully');
        return true;
      } else {
        AppUtils.showToastMessage('Failed To Create Doctor');
        return false;
      }
    } catch (e) {
      log('❌ Exception: $e');
      // AppUtils.showToastMessage('Something went wrong!');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<List<DoctorResult>> searchDoctor(String doctor) async {
    isLoading = true;
    update();
    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerSearchDoctor(doctor));
      if (response['success'] == true && response['results'] != null && response['results'].isNotEmpty) {
        log('✅ Found ${response['results'].length} doctors');
        log('✅ Found ${response['results']} doctors');
        ownerDoctorModel = OwnerDoctorModel.fromJson(response);
        return ownerDoctorModel?.results ?? [];
      } else {
        log('❌ No doctor found!');
        // AppUtils.showToastMessage('Doctor Not Found!');
        return [];
      }
    }catch(e){
      log('❌ Exception: $e');
      // AppUtils.showToastMessage('Something went wrong!');
      return [];
    }finally{
      isLoading = false;
      update();
    }
  }

  Future<void> fetchHospitals() async {

    isLoading = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerHospitalList);
      log('📥 API Response : ${jsonEncode(response)}');
      if (response["hospitalName"] != null) {
        final model = OwnerHospitalModel.fromJson(response);
        hospitalList = model.hospitalName ?? [];

        log("✅ Fetched ${hospitalList.length} hospitals");
      } else {
        log('❌ Error : ${response['message']}');
        AppUtils.showToastMessage("No hospitals found");
      }

    }catch (e){
      log("❌ Exception: $e");
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoading = false;
      update();
    }
  }

}