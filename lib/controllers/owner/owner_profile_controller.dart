import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/signinOption_screen.dart';

import '../../api/config.dart';
import '../../api/data_store.dart';
import '../../data/models/dentist/dentist_t&c_privacy_model.dart';
import '../../utils/app_utils.dart';
import '../../utils/enums.dart';

class OwnerProfileController extends GetxController implements GetxService{
  var isLoading = false.obs;
  var userData = {}.obs;
  int platformFee = 0;

  Future<void> fetchUserData() async{
    try{
      log('🔵 Stored user data : ${getData.read('userRecord')}');
      isLoading(true);
      String? storedUser = getData.read('userRecord');
      if(storedUser != null){
        userData.value = jsonDecode(storedUser);
      }
    }catch(e){
      WidgetsBinding.instance.addPostFrameCallback((_){
        //Get.snackbar('Error', 'Failed to fetch user profile');
      });
    }
    finally{
      isLoading(false);
      update();
    }
  }

  Future<void> updateProfileData(Map<String, dynamic> updatedData, File? profileImage) async {
    try{
      isLoading(true);
      update();

      String? userId = getData.read('userRecord')['id'];
      String? token = getData.read('token');

      log("🔵 User ID: $userId");
      log("🟢 Bearer Token: $token");

      if (userId == null || userId.isEmpty) {
        Get.snackbar("Error", "User ID not found",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      if (token == null || token.isEmpty) {
        Get.snackbar("Error", "Authorization token not found",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      var uri = Uri.parse('${Config.baseUrl}${Config.editProfile}');
      var request = http.MultipartRequest('Put', uri);

      //✅ Add updated profile data
      updatedData.forEach((key, value){
        if(value != null && value.toString().isNotEmpty){
          request.fields[key] = value.toString();
        }
      });
//✅ Pass id in the API request
//       request.fields['id'] = userId;
      //✅ Add updated pic
      if(profileImage != null){
        log("🟡 Profile Image Path: ${profileImage.path}");
        log("🟡 Profile Image Exists: ${await profileImage.exists()}");
        log("🟡 Profile Image Size: ${await profileImage.length()} bytes");
        final mimeType = lookupMimeType(profileImage.path) ?? "image/jpeg";
        request.files.add(await http.MultipartFile.fromPath("profileImage", profileImage.path,  contentType: MediaType.parse(mimeType),));
      }
      log("🟢 data: ${request.fields}");
      log("🟢 data: ${request.files.length}");
      request.headers.addAll({
        'Content-Type' : 'multipart/form-data',
        'Authorization' : 'Bearer $token'
      });

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      log("🔴 API Response Status Code: ${response.statusCode}");
      log("🔴 API Response Body: $responseData");

      if(response.statusCode == 200){
        var responseJson = jsonDecode(responseData);

        if(responseJson['userData'] != null){
          log("✅ Storing Updated User Data: ${responseJson["userData"]}");

          getData.write('userRecord', responseJson['userData']);
          userData.value = responseJson['userData'];
          update();
          AppUtils.showToastMessage('Profile updated successfully', toastType: ToastType.success);

          Get.back();
        } else {
          log("❌ API returned null user data!");
        }
      }
    }catch(e){
      log(" Exception : $e");
      Get.snackbar('Error', 'Something went wrong',
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      );
    }finally{
      isLoading(false);
      update();
    }
  }

  Future<void> deleteAccount() async {
    String? email = getData.read("userRecord")?["email"];

    if (email == null || email.isEmpty) {
      print("❌ No email found in local storage");
      return;
    }

    try {
      var response = await http.post(
        Uri.parse(Config.baseUrl + Config.deleteApi),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"email": email}),
      );

      if (response.statusCode == 200) {
        log("✅ Account deleted successfully");

        // Clear local storage and navigate to login screen
        getData.erase();
        // Get.off(const SigninOptionScreen());
        Get.offAll(() => const SigninOptionScreen());
      } else {
        log("❌ Failed to delete account: ${response.body}");
      }
    } catch (e) {
      log("❌ Exception: $e");
    }
  }

  Future<Settings?> fetchSettings() async {
    const String url = "${Config.baseUrl}settings";

    try {
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final settings = settingsFromJson(response.body);
        log("✅ Settings Fetched: ${settings.toJson()}");

        // ✅ Store the platform fee

        platformFee = (int.tryParse(settings.settings.platformFee) ?? 0);

        log("💰 Platform Fee Stored: $platformFee");

        return settings;
      } else {
        log("⚠️ Error: ${response.statusCode} - ${response.body}");
        return null;
      }
    } catch (e) {
      log("❌ Exception: $e");
      return null;
    }
  }

}