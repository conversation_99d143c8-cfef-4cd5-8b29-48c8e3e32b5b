import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:platix/api/config.dart';
import 'package:platix/data/models/owner/owner_deliveryboy_list.dart';
import 'package:platix/data/models/owner/owner_technician_list.dart';
import 'package:platix/services/api_service.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/owner/owner_bottombar.dart';
import '../../api/data_store.dart';
import '../../data/models/owner/owner_orders_data.dart';
import '../../data/models/owner/owner_report_details_info.dart';
import '../../data/models/user_model.dart';
import '../../utils/app_utils.dart';
import 'package:http/http.dart' as http;

class OwnerOrdersController extends GetxController implements GetxService{

  bool isLoadingOrders = false;
  bool isLoadingDetails = false;
  bool isLoading = false;
  OwnerOrders? ownerOrders;
  OwnerReportDetailsModel? ownerReportDetailsModel;
  OwnerDeliveryBoyModel? deliveryBoyModel;
  OwnerTechnicianModel? ownerTechnicianModel;
  List<Technician> technicianList = [];
  List<DeliveryBoy> deliveryBoyList = [];
  Technician? selectedTechnician;
  DeliveryBoy? selectedDeliveryBoy;
  RxBool cleanCurrentSelection = false.obs;
  RxBool cleanCurrentSelection1 = false.obs;
  bool cashFreeLoading = false;

  Future<void> fetchOrders({required String status}) async {

    isLoadingOrders = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerOrdersApi(status));
      log('📥 API Response : ${jsonEncode(response)}');

      if(response != null){

        List<dynamic> allOrders = [];

        // Extract all orders from different statuses
        response.forEach((key, value) {
          if (value is List) {
            allOrders.addAll(value);
          }
        });

        ownerOrders = OwnerOrders.fromJson(allOrders);
        if (allOrders.isEmpty) {
          log("📭 No orders available for status: $status");
        } else {
          log("✅ Orders Loaded Successfully");
          log("📦 Orders Data: $response");
        }
      }
      else{
        log("❌ ⚠ API Response is null or not a Map<String, dynamic>");
        AppUtils.showToastMessage('Failed to Load Data');
      }
    }catch (e){
      log("❌ Exception: $e");
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoadingOrders = false;
      update();
    }
  }

  Future<void> fetchRadiologyOrders({required String status}) async {

    isLoadingOrders = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerRadiologyOrders(status));
      print('*************************************');
      log('📥 API Response : ${jsonEncode(response)}');
      print('*************************************');

      if(response != null){

        List<dynamic> allOrders = [];

        // Extract all orders from different statuses
        response.forEach((key, value) {
          if (value is List) {
            allOrders.addAll(value);
          }
        });

        ownerOrders = OwnerOrders.fromJson(allOrders);
        if (allOrders.isEmpty) {
          log("📭 No orders available for status: $status");
        } else {
          log("✅ Orders Loaded Successfully");
          log("📦 Orders Data: $response");
        }
      }
      else{
        log("❌ ⚠ API Response is null or not a Map<String, dynamic>");
        AppUtils.showToastMessage('Failed to Load Data');
      }
    }catch (e){
      log("❌ Exception: $e");
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoadingOrders = false;
      update();
    }
  }

  Future<void> getOrderDetails({required id, required type}) async {
    isLoadingDetails = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerReportDetails(id, type));
      log('📥 API Response : ${jsonEncode(response)}');

      if(response != null && response.containsKey('data')){
        ownerReportDetailsModel = OwnerReportDetailsModel.fromJson(response);
        print('-------------------------------------------');
        log('✅ Report Details Loaded Successfully : ${jsonEncode(response)}');
        print('-------------------------------------------');
      }
      else{
        log("⚠️ API returned an empty response for getReport.");
        AppUtils.showToastMessage('Failed to load data');
      }
    }catch(e){
      log('❌ Exception : $e');
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoadingDetails = false;
      update();
    }
  }

  Future<bool> assignTechDel(Map<String, dynamic> data) async {
    isLoading = true;
    update();

    try {
      var response = await ApiService.postRequest(Config.baseUrl + Config.ownerAssignApi, data);
      log("📥 Request body : $data");
      log('📥 Api Response : ${jsonEncode(response)}');

      if (response != null && response['message'] == "Service assigned successfully") {
        AppUtils.showToastMessage('Assigned Successfully');
        return true;
      } else {
        log("${response?["message"]}");
        AppUtils.showToastMessage('Failed To Assign');
        return false;
      }
    } catch (e) {
      log('❌ Exception: $e');
      // AppUtils.showToastMessage('Something went wrong!');
      return false;
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> fetchTechnicians() async {

    isLoading = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerTechnicianList);
      log(Config.baseUrl + Config.ownerTechnicianList);
      log('📥 API Response : ${jsonEncode(response)}');
      if (response['success'] == true && response['technicians'] != null) {
        final model = OwnerTechnicianModel.fromJson(response);
        technicianList = model.technicians ?? [];

        log("✅ Fetched ${technicianList.length} technicians");
      } else {
        log('❌ Error : ${response['message']}');
        AppUtils.showToastMessage("No Technicians found");
      }

    }catch (e){
      log("❌ Exception: $e");
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoading = false;
      update();
    }
  }

  Future<void> fetchDeliveryBoys() async {

    isLoading = true;
    update();

    try{
      var response = await ApiService.getRequest(Config.baseUrl + Config.ownerDeliveryBoyList);
      log('📥 API Response : ${jsonEncode(response)}');
      if (response['success'] == true && response['DeliveryBoy'] != null) {
        final model = OwnerDeliveryBoyModel.fromJson(response);
        deliveryBoyList = model.deliveryBoy ?? [];

        log("✅ Fetched ${deliveryBoyList.length} delivery boys");
      } else {
        log('❌ Error : ${response['message']}');
        AppUtils.showToastMessage("No Delivery Boys found");
      }

    }catch (e){
      log("❌ Exception: $e");
      // AppUtils.showToastMessage('Something went wrong!');
    }
    finally{
      isLoading = false;
      update();
    }
  }


  Future<void> closeOrCancelOrder({required String status, required String orderId}) async {
    isLoading = true;
    update();

    try{
      Map<String, dynamic> body = {
        "id" : orderId
      };
      var response = await ApiService.putRequest(Config.baseUrl + Config.ownerCancelOrCancel(status), body);

      if(response != null){
        AppUtils.showToastMessage('${response["message"]}');
        log('Close or Cancelled :${response["message"]}');
        await fetchOrders(status: 'processing');
      }
    }catch (e){
      log('❌ Exception : $e');
      // AppUtils.showToastMessage('Something went wrong!');
    }finally{
      isLoading = false;
      update();
    }
  }

  final userRecord = UserRecord.fromJson(getData.read('userRecord'));


  Future<void> clearAllCancelled() async {
    String? token = getData.read("token");

    const String url = '${Config.baseUrl}${Config.ownerClearCancelled}';

    try {
      final response = await http.put(
        Uri.parse(url),
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
      );

      if (response.statusCode == 200) {
        log("✅ Successfully cleared all cancelled orders.");
        if(userRecord.organization?.organizationType?.organizationType == 'Radiology'){
          return await fetchRadiologyOrders(status: "cancelled");
        }

        else{
          return await fetchOrders(status: "cancelled");
        }
        update();
      } else {
        log("❌ Failed to clear orders. Status Code: ${response.statusCode}");
        log("Response Body: ${response.body}");
      }
    } catch (e) {
      log("⚠️ Error: $e");
    }
  }

  Future<void> clearAllCompleted() async {
    String? token = getData.read("token");

    const String url = '${Config.baseUrl}${Config.ownerClearCompleted}';

    try {
      final response = await http.put(
        Uri.parse(url),
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
      );

      if (response.statusCode == 200) {
        log("✅ Successfully cleared all completed orders.");

        update();
      } else {
        log("❌ Failed to clear orders. Status Code: ${response.statusCode}");
        log("Response Body: ${response.body}");
      }
    } catch (e) {
      log("⚠️ Error: $e");
    }
  }

Future<void> uploadImages(String id, List<XFile> images) async {
  isLoading = true;
  update();

  if (images.isEmpty) {
    AppUtils.showToastMessage('No images selected!');
    isLoading = false;
    update();
    return;
  }

  try {
    String? token = getData.read('token');
    if (token == null || token.isEmpty) {
      AppUtils.showToastMessage('Authorization token is missing!');
      return;
    }

    var url = Uri.parse(Config.baseUrl + Config.ownerImageUpload(id));
    var request = http.MultipartRequest('POST', url);

    // ✅ Ensure Authorization Header is correct
    request.headers.addAll({
      "Authorization": "Bearer $token",
      "Content-Type": "multipart/form-data"
    });

    // ✅ Attach multiple images
    for (var image in images) {
      final mimeType = lookupMimeType(image.path) ?? 'application/octet-stream';
      final mimeParts = mimeType.split('/'); // Extract type and subtype
      request.files.add(await http.MultipartFile.fromPath('images', image.path, contentType: MediaType(mimeParts[0], mimeParts[1])));
    }

    log('📤 Sending upload request to $url');

    // ✅ Send request
    var response = await request.send();
    var responseData = await http.Response.fromStream(response);

    log('📥 Response: ${response.statusCode} - ${responseData.body}');

    if (response.statusCode == 200) {
      var jsonResponse = jsonDecode(responseData.body);
      log("✅ Upload Success: ${jsonResponse['message']}");
      AppUtils.showToastMessage("Images uploaded successfully!");
      update();
    } else {
      log("❌ Upload Failed: ${response.statusCode} - ${responseData.body}");
      AppUtils.showToastMessage("Failed to upload images!");
    }

  } catch (e) {
    log('❌ Exception : $e');
    AppUtils.showToastMessage('Something went wrong!');
  } finally {
    isLoading = false;
    update();
  }
}

  Future<Map<String, String>?> createCashFreeOrder(Map<String, dynamic> request) async {

    String token = getData.read("token") ?? '';
    try {
      const String apiUrl = Config.baseUrl + Config.createCashFreeOrder;

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {
          'orderId': data["order_id"],
          'sessionId': data["payment_session_id"],
        };
      } else {
        Get.snackbar("Error", "Failed to Generate Order",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        log("Error: ${response.body}");
      }
    } catch (e) {
      // Get.snackbar("Error", "Something went wrong");
      log("Exception: $e");
    } finally {

    }
    return null;
  }


  Future<void> checkStatus(String orderId) async {
    String token = getData.read("token") ?? '';
    try {
      isLoading = true;
      String apiUrl = Config.baseUrl+Config.checkPaymentStatus(orderId);

      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          "Content-Type": "application/json",
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {

      } else {
        Get.snackbar("Error", "Failed to get Order Status",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        log("Error: ${response.body}");
      }
    } catch (e) {
      // Get.snackbar("Error", "Something went wrong");
      log("Exception: $e");
    } finally {
      isLoading = false;
    }
  }

  Future<void> makePayment({
    required String orderId,
    required String transactionId,
    required String amount,
  }) async {

    String? token = getData.read("token");

    const String apiUrl = "${Config.baseUrl}owner/paynow"; // Update if needed

    final Map<String, dynamic> body = {
      "orderId": orderId,
      "transactionId": transactionId,
      "amount": amount,
    };

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer $token",
        },
        body: jsonEncode(body),
      );

      if (response.statusCode == 200) {
        log("Payment successful: ${response.body}");
        Get.offAll(() => const OwnerBottomBar());
      } else {
        log("Payment failed: ${response.statusCode} - ${response.body}");
      }
    } catch (e) {
      log("Error: $e");
    }
  }

}