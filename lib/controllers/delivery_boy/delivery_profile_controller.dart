import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/enums.dart';

import '../../api/config.dart';
import '../../api/data_store.dart';
import '../../view/screens/signinOption_screen.dart';
import 'package:image/image.dart' as img;

class DeliveryProfileController extends GetxController implements GetxService{
  var isLoading = false.obs;
  var userData = {}.obs;

  Future<void> fetchUserData() async{
    try{
      log('🔵 Stored user data : ${getData.read('userRecord')}');
      isLoading(true);
      String? storedUser = getData.read('userRecord');
      if(storedUser != null){
        userData.value = jsonDecode(storedUser);
      }
    }catch(e){
      WidgetsBinding.instance.addPostFrameCallback((_){
        //Get.snackbar('Error', 'Failed to fetch user profile');
      });
    }
    finally{
      isLoading(false);
      update();
    }
  }
  // Future<bool> updateUserProfile(Map<String, dynamic> updatedData, XFile? pickedFile) async {
  //   try {
  //     isLoading(true);
  //     update();
  //
  //     String? userId = getData.read("userRecord")?["id"];
  //     String? token = getData.read("token");
  //
  //     if (userId == null || userId.isEmpty || token == null || token.isEmpty) {
  //       Get.snackbar("Error", "Missing User ID or Token");
  //       return false;
  //     }
  //
  //     var uri = Uri.parse("https://platix-server.vercel.app/profile/edit");
  //     var request = http.MultipartRequest("PUT", uri);
  //     request.fields["id"] = userId;
  //
  //     updatedData.forEach((key, value) {
  //       if (value != null && value.toString().isNotEmpty) {
  //         request.fields[key] = value.toString();
  //       }
  //     });
  //
  //     // ✅ Handle Image Upload Properly
  //     if (pickedFile != null) {
  //
  //       if (kIsWeb) {
  //         Uint8List imageBytes = await pickedFile.readAsBytes();
  //         String? mimeType = lookupMimeType(pickedFile.path) ?? "image/jpeg";
  //
  //         var multipartFile = http.MultipartFile.fromBytes(
  //           'profileImage',
  //           imageBytes,
  //           filename: 'profile.${mimeType.split('/').last}',
  //           contentType: MediaType.parse(mimeType),
  //         );
  //         request.files.add(multipartFile);
  //       } else {
  //             final imageBytes = await pickedFile.readAsBytes();
  //             final mimeType = lookupMimeType(pickedFile.path) ?? "image/jpeg";
  //
  //             final multipartFile = http.MultipartFile.fromBytes(
  //               'profileImage', // or 'profile_image' if that's what backend expects
  //               imageBytes,
  //               filename: 'profile.jpg',
  //               contentType: MediaType.parse(mimeType),
  //             );
  //             request.files.add(multipartFile);
  //       }
  //     }
  //
  //     request.headers.addAll({
  //       "Authorization": "Bearer $token",
  //     });
  //
  //     var response = await request.send();
  //     var responseData = await response.stream.bytesToString();
  //
  //     print("🔴 API Response Status Code: ${response.statusCode}");
  //     print("🔴 API Response Headers: ${response.headers}");
  //     print("🔴 API Response Body: $responseData");
  //
  //     if (response.statusCode == 200) {
  //       var responseJson = jsonDecode(responseData);
  //       if (responseJson["data"] != null) {
  //         getData.write("userRecord", responseJson["data"]);
  //         userData.value = responseJson["data"];
  //         update();
  //         return true;
  //       } else {
  //         print("❌ API returned null user data!");
  //         return false;
  //       }
  //     } else {
  //       Get.snackbar("Error", "Server error: $responseData");
  //       return false;
  //     }
  //   } catch (e) {
  //     print("❌ Exception: $e");
  //     Get.snackbar("Error", "Something went wrong: $e");
  //     return false;
  //   } finally {
  //     isLoading(false);
  //     update();
  //   }
  // }

  Future<void> updateProfileData(Map<String, dynamic> updatedData, File? profileImage) async {
    try{
      isLoading(true);
      update();

      String? userId = getData.read('userRecord')['id'];
      String? token = getData.read('token');

      log("🔵 User ID: $userId");
      log("🟢 Bearer Token: $token");

      if (userId == null || userId.isEmpty) {
        Get.snackbar("Error", "User ID not found",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      if (token == null || token.isEmpty) {
        Get.snackbar("Error", "Authorization token not found",
          backgroundColor: AppColors.primary3,
          colorText: Colors.white,
          duration: const Duration(seconds: 3),
          snackPosition: SnackPosition.TOP,
        );
        return;
      }

      var uri = Uri.parse('${Config.baseUrl}${Config.editProfile}');
      var request = http.MultipartRequest('Put', uri);

      //✅ Add updated profile data
      updatedData.forEach((key, value){
        if(value != null && value.toString().isNotEmpty){
          request.fields[key] = value.toString();
        }
      });
//✅ Pass id in the API request
//       request.fields['id'] = userId;
      //✅ Add updated pic
      if(profileImage != null){
        log("🟡 Profile Image Path: ${profileImage.path}");
        log("🟡 Profile Image Exists: ${await profileImage.exists()}");
        log("🟡 Profile Image Size: ${await profileImage.length()} bytes");
        final mimeType = lookupMimeType(profileImage.path) ?? "image/jpeg";
        request.files.add(await http.MultipartFile.fromPath("profileImage", profileImage.path,  contentType: MediaType.parse(mimeType),));
      }
      log("🟢 data: ${request.fields}");
      log("🟢 data: ${request.files.length}");
      request.headers.addAll({
        'Content-Type' : 'multipart/form-data',
        'Authorization' : 'Bearer $token'
      });

      var response = await request.send();
      var responseData = await response.stream.bytesToString();

      log("🔴 API Response Status Code: ${response.statusCode}");
      log("🔴 API Response Body: $responseData");

      if(response.statusCode == 200){
        var responseJson = jsonDecode(responseData);
        //Get.snackbar("Successfully","Updated user");

        if(responseJson['userData'] != null){
          //Get.snackbar("Successfully","Updated user");
          log("✅ Storing Updated User Data: ${responseJson["userData"]}");
          getData.write('userRecord', responseJson['userData']);
          userData.value = responseJson['userData'];
          //await Future.delayed(const Duration(milliseconds: 500));
          update();
          // ✅ Show snackbar for success
          AppUtils.showToastMessage('Profile updated successfully', toastType: ToastType.success);
          Get.back();

          //await Future.delayed(const Duration(milliseconds: 500));
        } else {
          log("❌ API returned null user data!");
        }
      }
    }catch(e){
      log(" Exception : $e");
      Get.snackbar('Error', 'Something went wrong',
        backgroundColor: AppColors.primary3,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
        snackPosition: SnackPosition.TOP,
      );
    }finally{
      isLoading(false);
      update();
    }
  }

  Future<void> deleteAccount() async {
    String? email = getData.read("userRecord")?["email"];

    if (email == null || email.isEmpty) {
      print("❌ No email found in local storage");
      return;
    }

    try {
      var response = await http.post(
        Uri.parse(Config.baseUrl + Config.deleteApi),
        headers: {"Content-Type": "application/json"},
        body: jsonEncode({"email": email}),
      );

      if (response.statusCode == 200) {
        log("✅ Account deleted successfully");

        // Clear local storage and navigate to login screen
        getData.erase();
        // Get.off(const SigninOptionScreen());
        Get.offAll(() => const SigninOptionScreen());
      } else {
        log("❌ Failed to delete account: ${response.body}");
      }
    } catch (e) {
      log("❌ Exception: $e");
    }
  }
}